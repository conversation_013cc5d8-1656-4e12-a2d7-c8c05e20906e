#!/bin/bash

# Color codes for better output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to make API calls and format output
call_api() {
  local test_num=$1
  local description=$2
  local data=$3

  echo -e "\n${YELLOW}${test_num}. ${description}${NC}"
  echo "Request: ${data}"
  # Capture both HTTP status code and response body
  local response=$(curl -s -w "\n%{http_code}" -X POST http://localhost:8080/risk/assess \
    -H "Content-Type: application/json" \
    -d "$data")
  
  # Extract status code (last line) and response body (all previous lines)
  local status_code=$(echo "$response" | tail -n1)
  local body=$(echo "$response" | sed '$d')
  
  echo "Status Code: ${status_code}"
  echo "Response:"
  echo "$body" | json_pp
  echo "----------------------------------------"
}

echo -e "${GREEN}Testing risk/assess API with various phone number formats${NC}"
echo "=========================================================="

# 1. Standard phone numbers (LOW risk)
call_api "1" "Test with standard China Mobile number (139)" '{"phoneNumbers": ["13912345678"]}'
call_api "2" "Test with standard China Unicom number (130)" '{"phoneNumbers": ["13012345678"]}'
call_api "3" "Test with standard China Telecom number (133)" '{"phoneNumbers": ["13312345678"]}'

# 2. Phone number formats
call_api "4" "Test with +86 prefix" '{"phoneNumbers": ["+8613800138000"]}'
call_api "5" "Test with 86 prefix (no plus)" '{"phoneNumbers": ["8613800138000"]}'
call_api "6" "Test with multiple formats of same number" '{"phoneNumbers": ["13800138000", "+8613800138000", "8613800138000"]}'

# 3. China Mobile virtual numbers (HIGH risk, FRAUD type)
call_api "7" "Test China Mobile virtual numbers (165, 1703, 1705, 1706)" \
  '{"phoneNumbers": ["16512345678", "17031234567", "17051234567", "17061234567"]}'

# 4. China Unicom virtual numbers (HIGH risk, FRAUD type)
call_api "8" "Test China Unicom virtual numbers (167, 1704, 1707-1709, 171)" \
  '{"phoneNumbers": ["16712345678", "17041234567", "17071234567", "17081234567", "17091234567", "17112345678"]}'

# 5. China Telecom virtual numbers (HIGH risk, FRAUD type)
call_api "9" "Test China Telecom virtual numbers (162, 1700-1702)" \
  '{"phoneNumbers": ["16212345678", "17001234567", "17011234567", "17021234567"]}'

# 6. China Broadcast virtual numbers (HIGH risk, FRAUD type)
call_api "10" "Test China Broadcast virtual number (192)" \
  '{"phoneNumbers": ["19212345678"]}'

# 7. Invalid number formats
call_api "11" "Test with empty string" '{"phoneNumbers": [""]}'
call_api "12" "Test with null in array" '{"phoneNumbers": [null]}'
call_api "13" "Test with non-numeric characters" '{"phoneNumbers": ["13abc456789"]}'
call_api "14" "Test with special characters" '{"phoneNumbers": ["1380013*#@!"]}'
call_api "15" "Test with too short number (9 digits)" '{"phoneNumbers": ["138001380"]}'
call_api "16" "Test with too long number (12 digits)" '{"phoneNumbers": ["138001380000"]}'
call_api "17" "Test with international number" '{"phoneNumbers": ["+14155552671"]}'

# 8. Edge cases
call_api "18" "Test with empty array" '{"phoneNumbers": []}'
call_api "19" "Test with empty JSON object" '{}'
call_api "20" "Test with invalid JSON" '{"phoneNumbers": ["13800138000"}'

# 9. Mixed test cases
call_api "21" "Test with mixed number types" \
  '{"phoneNumbers": ["13800138000", "16512345678", "19212345678", "12345", "", "invalid"]}'

call_api "22" "Test with large batch (10 numbers)" \
  '{"phoneNumbers": ["13800138001","13800138002","13800138003","13800138004","13800138005","16512345678","16712345678","17011234567","19212345678","12345"]}'

# 10. Different content types
call_api "23" "Test with text/plain content type" \
  '{"phoneNumbers": ["13800138000"]}'

call_api "24" "Test with no content type header" \
  '{"phoneNumbers": ["13800138000"]}'

# 11. Phone numbers with spaces (blanks)
call_api "25" "Test with spaces in virtual phone numbers (leading, middle, trailing)" \
  '{"phoneNumbers": [" 16512345678", "1651 2345678", "16512345678 ", "1670 1234 567", " 1921 2345 678 "]}'

echo -e "${GREEN}All tests completed!${NC}"
