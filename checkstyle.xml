<?xml version="1.0"?>
<!DOCTYPE module PUBLIC "-//Checkstyle//DTD Checkstyle Configuration 1.3//EN"
        "https://checkstyle.org/dtds/configuration_1_3.dtd">

<!--
  Kerry Properties代码质量检查规则集
  核心目标：保持代码一致性、可维护性和安全性
  主要包含：代码格式、命名规范、文档规范、安全实践等检查项
-->
<module name="Checker">
    <!-- 基础配置 -->
    <property name="charset" value="UTF-8"/>
    <property name="severity" value="error"/>
    <property name="fileExtensions" value="java,properties,xml"/>

    <!-- 抑制规则配置 -->
    <module name="SuppressionFilter">
        <property name="file" value="suppressions.xml"/>
    </module>

    <!-- 文件级检查 -->
    <module name="FileLength">
        <property name="max" value="1000"/>
    </module>

    <module name="JavadocPackage"/>

    <!-- 行级格式检查 -->
    <module name="LineLength">
        <property name="max" value="125"/>
        <property name="ignorePattern"
                  value="^package.*|^import.*|href\s*=\s*&quot;[^&quot;]*&quot;|http://|https://|ftp://"/>
    </module>

    <!-- 语法树检查（TreeWalker） -->
    <module name="TreeWalker">
        <!-- 文件结构检查 -->
        <module name="OuterTypeFilename"/>
        <module name="OneTopLevelClass"/>
        <module name="ModifierOrder"/>
        <module name="RedundantModifier"/>
        <!-- 注解规范 -->
        <module name="AnnotationUseStyle">
            <property name="elementStyle" value="compact"/>
        </module>
        <module name="MissingOverride"/>
        <module name="PackageAnnotation"/>
        <module name="AnnotationLocation">
            <property name="allowSamelineSingleParameterlessAnnotation" value="true"/>
        </module>

        <!-- 要求测试方法必须有@DisplayName注解，但不限制顺序 -->
        <module name="RegexpSinglelineJava">
            <property name="format" value="(^|\s)(@Test)(\s|\()(?!([^}]*@DisplayName|.*\{[^}]*@DisplayName))"/>
            <property name="message" value="测试方法必须使用@DisplayName注解提供清晰的测试描述"/>
        </module>
        <module name="RegexpSinglelineJava">
            <property name="format"
                      value="(^|\s)(@ParameterizedTest)(\s|\()(?!([^}]*@DisplayName|.*\{[^}]*@DisplayName))"/>
            <property name="message" value="参数化测试方法必须使用@DisplayName注解提供清晰的测试描述"/>
        </module>

        <!-- record变量命名规范 -->
        <module name="RecordComponentName">
            <property name="format" value="^[a-z][a-zA-Z0-9]*$"/>
        </module>
        <!-- 禁用系统级操作 -->
        <module name="RegexpSinglelineJava">
            <property name="format" value="System\.(exit|gc)"/>
            <property name="message" value="禁止直接调用系统级操作"/>
        </module>
        <!-- 建议增加日志检查 -->
        <module name="RegexpSinglelineJava">
            <property name="format" value="System\.out\.println"/>
            <property name="message" value="必须使用SLF4J日志组件"/>
        </module>
        <!-- 代码块规范 -->
        <module name="EmptyBlock">
            <property name="option" value="text"/>
        </module>
        <module name="LeftCurly">
            <property name="option" value="eol"/>
        </module>
        <module name="RightCurly">
            <property name="option" value="same"/>
        </module>
        <module name="NeedBraces"/>
        <module name="AvoidNestedBlocks"/>
        <module name="IllegalCatch">
            <property name="illegalClassNames"
                      value="java.lang.Exception, java.lang.Throwable, java.lang.RuntimeException"/>
        </module>
        <module name="RegexpSinglelineJava">
            <property name="format" value="printStackTrace"/>
            <property name="message" value="Avoid exposing stack traces directly"/>
        </module>

        <!-- 类设计规范 -->
        <module name="FinalClass"/>
        <module name="HideUtilityClassConstructor">
            <property name="ignoreAnnotatedBy"
                      value="SpringBootApplication,Configuration,UtilityClass"/>
        </module>
        <module name="InterfaceIsType"/>
        <module name="MutableException"/>
        <module name="InnerTypeLast"/>

        <!-- 编码规范 -->
        <module name="ArrayTypeStyle"/>
        <module name="CovariantEquals"/>
        <module name="DefaultComesLast"/>
        <module name="EmptyStatement"/>
        <module name="EqualsHashCode"/>
        <module name="FallThrough"/>
        <module name="IllegalInstantiation"/>
        <module name="InnerAssignment"/>
        <module name="MissingSwitchDefault"/>
        <module name="ModifiedControlVariable"/>
        <module name="SimplifyBooleanExpression"/>
        <module name="SimplifyBooleanReturn"/>
        <module name="StringLiteralEquality"/>
        <module name="CyclomaticComplexity">
            <property name="max" value="10"/>
        </module>
        <module name="ExecutableStatementCount">
            <property name="max" value="50"/>
        </module>
        <module name="ParameterNumber">
            <property name="max" value="6"/>
        </module>
        <module name="NestedForDepth">
            <property name="max" value="3"/>
        </module>
        <module name="NestedIfDepth">
            <property name="max" value="3"/>
        </module>
        <module name="NestedTryDepth">
            <property name="max" value="3"/>
        </module>
        <module name="NoClone"/>
        <module name="NoFinalizer"/>
        <module name="SuperClone"/>
        <module name="SuperFinalize"/>
        <module name="AvoidDoubleBraceInitialization"/>

        <!-- 命名规范 -->
        <module name="AbbreviationAsWordInName">
            <property name="allowedAbbreviationLength" value="0"/>
            <property name="tokens"
                      value="CLASS_DEF,INTERFACE_DEF,ENUM_DEF,ANNOTATION_DEF,ANNOTATION_FIELD_DEF,
                                      PARAMETER_DEF,VARIABLE_DEF,METHOD_DEF"/>
            <property name="allowedAbbreviations" value="DTO,XML,URL,URI,ID,UUID,API,VO,CRM,JPA,JWT"/>
        </module>
        <module name="AbstractClassName">
            <property name="format" value="^(Abstract.*|Base.*)$"/>
        </module>
        <module name="ClassTypeParameterName"/>
        <module name="ConstantName"/>
        <module name="LocalFinalVariableName"/>
        <module name="LocalVariableName"/>
        <module name="MemberName"/>
        <module name="MethodName">
            <property name="format" value="^[a-z][a-zA-Z0-9_]*$"/>
        </module>
        <module name="MethodTypeParameterName"/>
        <module name="InterfaceTypeParameterName"/>
        <module name="PackageName"/>
        <module name="ParameterName"/>
        <module name="StaticVariableName"/>
        <module name="TypeName"/>

        <!-- 导入规范 -->
        <module name="AvoidStarImport">
            <property name="excludes"
                      value="java.awt.*,javax.swing.*,org.junit.jupiter.api.*,org.mockito.Mockito.*,org.junit.jupiter.api.Assertions.*"/>
        </module>
        <module name="IllegalImport"/>
        <module name="RedundantImport"/>
        <module name="UnusedImports"/>

        <!-- 空白字符规范 -->
        <module name="EmptyForIteratorPad"/>
        <module name="GenericWhitespace"/>
        <module name="MethodParamPad"/>
        <module name="NoWhitespaceAfter"/>
        <module name="NoWhitespaceBefore"/>
        <module name="OperatorWrap">
            <property name="option" value="eol"/>
            <property name="tokens"
                      value="PLUS,MINUS,BAND,BOR,BXOR,LAND,LOR,QUESTION,COLON,EQUAL,NOT_EQUAL,DIV,PLUS_ASSIGN,MINUS_ASSIGN,STAR_ASSIGN,DIV_ASSIGN,MOD_ASSIGN,SR_ASSIGN,BSR_ASSIGN,SL_ASSIGN,BXOR_ASSIGN,BOR_ASSIGN,BAND_ASSIGN"/>
        </module>
        <module name="ParenPad"/>
        <module name="TypecastParenPad"/>
        <module name="WhitespaceAfter"/>
        <module name="WhitespaceAround"/>

        <!-- Javadoc规范 -->
        <module name="JavadocMethod">
            <property name="accessModifiers" value="public,protected"/>
            <property name="allowMissingParamTags" value="true"/>
            <property name="allowMissingReturnTag" value="true"/>
            <property name="allowedAnnotations"
                      value="Override,Test,BeforeEach,AfterEach,BeforeAll,AfterAll"/>
        </module>
        <module name="JavadocType"/>
        <module name="JavadocVariable">
            <property name="scope" value="public"/>
        </module>
        <module name="JavadocStyle"/>
        <module name="NonEmptyAtclauseDescription"/>
        <module name="JavadocTagContinuationIndentation">
            <property name="offset" value="0"/>
        </module>
        <module name="SummaryJavadoc"/>
        <module name="JavadocParagraph">
            <property name="allowNewlineParagraph" value="true"/>
        </module>
        <module name="AtclauseOrder">
            <property name="target" value="CLASS_DEF,INTERFACE_DEF,ENUM_DEF,METHOD_DEF,CTOR_DEF"/>
            <property name="tagOrder" value="@param, @return, @throws,@author, @see, @since"/>
        </module>
    </module>
</module>