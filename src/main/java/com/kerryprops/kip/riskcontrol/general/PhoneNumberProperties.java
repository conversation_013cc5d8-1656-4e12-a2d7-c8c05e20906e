package com.kerryprops.kip.riskcontrol.general;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Configuration properties for phone number validation.
 */
@Data
@Component
@ConfigurationProperties(prefix = "phone")
public class PhoneNumberProperties {

    private VirtualPrefixes virtualPrefixes;

    @Data
    public static class VirtualPrefixes {
        private List<String> chinaMobile;
        private List<String> chinaUnicom;
        private List<String> chinaTelecom;
        private List<String> chinaBroadcast;
    }
}