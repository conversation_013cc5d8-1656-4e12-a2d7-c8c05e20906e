package com.kerryprops.kip.riskcontrol.general;

import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Request model for phone risk assessment.
 * Contains a list of phone numbers to be assessed.
 */
@Getter
@Setter
public class PhoneRiskAssessRequest {

    @NotEmpty(message = "Phone numbers list cannot be empty")
    private List<String> phoneNumbers;

}
