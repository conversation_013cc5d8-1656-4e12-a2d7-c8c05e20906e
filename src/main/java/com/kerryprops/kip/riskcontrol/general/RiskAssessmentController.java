package com.kerryprops.kip.riskcontrol.general;

import com.kerryprops.kip.exception.BadRequestException;
import com.kerryprops.kip.riskcontrol.shared.ComprehensiveRiskAssessmentService;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Controller for risk assessment API endpoints.
 * Provides comprehensive risk assessment capabilities including phone number validation,
 * IP geolocation-based assessment, and Tencent Cloud risk evaluation.
 *
 * <p>For detailed documentation, see: <a href="https://kerryprops.atlassian.net/wiki/spaces/TAIC/pages/675840071/S+-">...</a>
 */
@Slf4j
@RestController
@Validated
@RequestMapping("/risk")
@Tag(name = "Kerry风险评估", description = "Kerry Properties风险评估服务，包括手机号风险评估、IP地址地理位置风险评估和腾讯云风控服务")
public class RiskAssessmentController {

    private final RiskAssessmentService riskAssessmentService;

    private final TencentCloudService tencentCloudService;

    @Autowired(required = false)
    private ComprehensiveRiskAssessmentService comprehensiveRiskAssessmentService;

    public RiskAssessmentController(RiskAssessmentService riskAssessmentService, TencentCloudService tencentCloudService) {
        this.riskAssessmentService = riskAssessmentService;
        this.tencentCloudService = tencentCloudService;
    }

    /**
     * Endpoint to assess risk for phone numbers.
     *
     * @param request       the request containing phone numbers to assess
     * @param bindingResult validation result
     * @return response entity with assessment results or error
     */
    @Operation(summary = "批量手机号风险评估", description = "通过是否高危虚拟号评估风险")
    @PostMapping("/assess")
    public List<PhoneRiskAssessResponse> assessPhoneNumbers(@Valid @NotNull @RequestBody PhoneRiskAssessRequest request,
                                                            BindingResult bindingResult) {

        log.info("Received risk assessment request for {} phone numbers", Optional.of(request.getPhoneNumbers())
                .map(List::size)
                .orElse(0));

        // Validate request for NotEmpty
        if (bindingResult.hasErrors()) {
            var errorMessage = bindingResult.getAllErrors()
                    .stream()
                    .map(DefaultMessageSourceResolvable::getDefaultMessage)
                    .filter(Objects::nonNull)
                    .findFirst()
                    .orElse("Invalid request");

            log.warn("Validation error: {}", errorMessage);
            throw new BadRequestException();
        }

        // Validate phone numbers according to the new pattern
        List<PhoneRiskAssessResponse> results = new ArrayList<>();
        for (String originPhoneNumber : request.getPhoneNumbers()) {
            String phoneNumber = StringUtils.deleteWhitespace(originPhoneNumber);
            if (isValidPhoneNumber(phoneNumber)) {
                // Process valid phone numbers with the service
                results.add(riskAssessmentService.assessPhoneNumber(phoneNumber));
                log.info("Processing valid phone number: {}", phoneNumber);
            } else {
                // Return LOW risk level for all invalid phone numbers
                // This includes numbers with different lengths or not starting with 1
                results.add(createLowRiskResponse(phoneNumber));
                log.warn("Invalid phone number format: {}, returning LOW risk level", phoneNumber);
            }
        }

        log.info("Successfully processed risk assessment for {} phone numbers", results.size());
        return results;
    }

    @Operation(summary = "腾讯云风控", description = "通过腾讯风控引擎对用户信息进行风控评估")
    @PostMapping("/tencent-assess")
    public TxRiskResponse assessPhoneNumber(@Valid @RequestBody PhoneTxRiskAssessRequest request)
            throws TencentCloudSDKException {
        return tencentCloudService.assessPhoneRisk(request);
    }

    /**
     * Validates if a phone number matches the required pattern.
     * Valid phone numbers must:
     * 1. Have an optional +86 or 86 prefix (+ is optional in +86)
     * 2. Be 11 digits without prefix
     * 3. Start with 1 as the first digit of the 11-digit number
     *
     * @param phoneNumber the phone number to validate
     * @return true if the phone number is valid, false otherwise
     */
    private boolean isValidPhoneNumber(String phoneNumber) {
        if (StringUtils.isBlank(phoneNumber)) {
            return false;
        }
        return PhoneNumberUtil.PHONE_PATTERN.matcher(phoneNumber)
                .matches();
    }

    /**
     * Creates a response with LOW risk level for invalid phone numbers.
     * Handles empty or null phone numbers by using empty string as a default.
     *
     * @param phoneNumber the phone number, can be null or empty
     * @return the risk assessment response with LOW risk
     */
    private PhoneRiskAssessResponse createLowRiskResponse(String phoneNumber) {
        // Use empty string if phoneNumber is null to avoid NullPointerException
        String safePhoneNumber = phoneNumber != null ? phoneNumber : "";

        return new PhoneRiskAssessResponse(safePhoneNumber, RiskLevel.LOW, Collections.singletonList(RiskType.NONE),
                LocalDateTime.now());
    }

}
