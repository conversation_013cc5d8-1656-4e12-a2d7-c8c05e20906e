package com.kerryprops.kip.riskcontrol.general;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * Service interface for risk assessment operations.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RiskAssessmentService {

    private final PhoneNumberUtil phoneNumberUtil;

    public List<PhoneRiskAssessResponse> assessPhoneNumbers(List<String> phoneNumbers) {
        log.info("Starting batch risk assessment for {} phone numbers", phoneNumbers.size());
        var results = phoneNumbers.stream()
                .map(this::assessPhoneNumber)
                .toList();
        log.info("Completed batch risk assessment - processed: {}, results: {}", phoneNumbers.size(), results.size());
        return results;
    }

    public PhoneRiskAssessResponse assessPhoneNumber(String phoneNumber) {
        log.info("Starting risk assessment for phone number: {}", phoneNumber);

        if (!phoneNumberUtil.isValidPhoneNumber(phoneNumber)) {
            log.warn("Invalid phone number format - number: {}, reason: Not compliant with E164 format", phoneNumber);
            return createUnknownRiskResponse(phoneNumber);
        }

        var normalizedNumber = phoneNumberUtil.normalizePhoneNumber(phoneNumber);
        log.info("Number normalization - original: {}, normalized: {}", phoneNumber, normalizedNumber);

        var isVirtual = phoneNumberUtil.isVirtualNumber(normalizedNumber);
        log.info("Virtual number detection - number: {}, isVirtual: {}", normalizedNumber, isVirtual);

        var riskLevel = determineRiskLevel(isVirtual);
        var riskTypes = determineRiskTypes(isVirtual);

        log.info("Risk assessment completed - number: {}, riskLevel: {}, riskTypes: {}, criteria: virtualNumber={}",
                phoneNumber, riskLevel, riskTypes, isVirtual);

        return new PhoneRiskAssessResponse(phoneNumber, riskLevel, riskTypes, LocalDateTime.now());
    }

    /**
     * Determines the risk level based on whether the number is virtual and its
     * carrier.
     *
     * @param isVirtual whether the number is a virtual number
     * @return the determined risk level
     */
    private RiskLevel determineRiskLevel(boolean isVirtual) {
        log.debug("Determining risk level - isVirtual: {}", isVirtual);
        var riskLevel = !isVirtual ? RiskLevel.LOW : RiskLevel.HIGH;
        log.debug("Risk level determined - isVirtual: {}, result: {}", isVirtual, riskLevel);
        return riskLevel;
    }

    /**
     * Determines the risk types based on whether the number is virtual and its
     * carrier.
     *
     * @param isVirtual whether the number is a virtual number
     * @return the list of determined risk types
     */
    private List<RiskType> determineRiskTypes(boolean isVirtual) {
        log.debug("Determining risk types - isVirtual: {}", isVirtual);
        var riskTypes = !isVirtual ? Collections.singletonList(RiskType.NONE) : List.of(RiskType.FRAUD);
        log.debug("Risk types determined - isVirtual: {}, types: {}", isVirtual, riskTypes);
        return riskTypes;
    }

    /**
     * Creates a response for unknown risk assessment.
     *
     * @param phoneNumber the phone number
     * @return the risk assessment response with unknown risk
     */
    private PhoneRiskAssessResponse createUnknownRiskResponse(String phoneNumber) {
        log.debug("Creating unknown risk response for number: {}", phoneNumber);
        var response =
                new PhoneRiskAssessResponse(phoneNumber, RiskLevel.UNKNOWN, Collections.singletonList(RiskType.NONE),
                        LocalDateTime.now());
        log.debug("Unknown risk response created - number: {}, response: {}", phoneNumber, response);
        return response;
    }

}
