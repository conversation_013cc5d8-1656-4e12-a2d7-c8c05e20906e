package com.kerryprops.kip.riskcontrol.general;

import com.kerryprops.kip.exception.ApiAccessException;

/**
 * RiskResult.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Yu 2025-05-20 10:00:55
 **/
public enum RiskResult {
    /**
     * 无恶意.
     */
    PASS("pass"),
    /**
     * 低风险，需要人工审核.
     */
    REVIEW("review"),
    /**
     * 高风险，建议拦截.
     */
    REJECT("reject");

    private final String txRiskLevel;

    RiskResult(String txRiskLevel) {
        this.txRiskLevel = txRiskLevel;
    }

    public static RiskResult of(String txRiskResult) {
        for (RiskResult value : RiskResult.values()) {
            if (value.txRiskLevel.equals(txRiskResult)) {
                return value;
            }
        }
        throw new ApiAccessException("unknown txRiskResult");
    }
}
