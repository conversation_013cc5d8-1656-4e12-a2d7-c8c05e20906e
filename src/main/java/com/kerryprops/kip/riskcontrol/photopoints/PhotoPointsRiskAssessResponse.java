package com.kerryprops.kip.riskcontrol.photopoints;

import com.kerryprops.kip.riskcontrol.general.RiskResult;
import com.kerryprops.kip.riskcontrol.shared.IpGeolocationInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Photo points risk assessment response.
 */
@Data
@Schema(description = "Photo points risk assessment response")
public class PhotoPointsRiskAssessResponse {

    @Schema(description = "Mall code", example = "JAKC")
    private String mallCode;

    @Schema(description = "IP address", example = "************")
    private String ipAddress;

    @Schema(description = "IP geolocation information")
    private IpGeolocationInfo ipGeolocation;

    @Schema(description = "Phone number", example = "13800138000")
    private String phoneNumber;

    @Schema(description = "Risk assessment result", example = "PASS")
    private RiskResult riskResult;

    @Schema(description = "Block message if rejected", example = "该功能无法使用，请至礼宾台")
    private String blockMessage;

    @Schema(description = "Assessment details", example = "拍照积分：非大陆IP被拦截")
    private String assessmentDetails;

    @Schema(description = "Assessment time")
    private LocalDateTime assessmentTime;
}
