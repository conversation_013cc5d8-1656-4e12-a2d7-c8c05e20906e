package com.kerryprops.kip.riskcontrol.photopoints;

import com.kerryprops.kip.riskcontrol.shared.RiskAssessmentContext;
import com.kerryprops.kip.riskcontrol.shared.BaseRiskAssessmentService;
import com.kerryprops.kip.riskcontrol.shared.ComprehensiveRiskAssessmentService;
import com.kerryprops.kip.riskcontrol.shared.IpGeolocation;
import com.kerryprops.kip.riskcontrol.shared.IpGeolocationInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * Service for photo points risk assessment.
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "drools.enabled", havingValue = "true", matchIfMissing = true)
public class PhotoPointsRiskAssessmentService extends BaseRiskAssessmentService {

    private final ComprehensiveRiskAssessmentService comprehensiveRiskAssessmentService;

    /**
     * Assess photo points risk.
     */
    public PhotoPointsRiskAssessResponse assessPhotoPointsRisk(PhotoPointsRiskAssessRequest request) {
        log.info("Starting photo points risk assessment - mall: {}, IP: {}, phone: {}",
                request.getMallCode(), request.getIpAddress(), request.getPhoneNumber());

        // Validate request
        validateCommonParameters(request.getMallCode());

        // Create context
        RiskAssessmentContext context = createBaseRiskContext(
                request.getMallCode(),
                RiskAssessmentContext.BusinessScenario.PHOTO_POINTS
        );

        // Add IP info
        if (request.getIpAddress() != null) {
            context = withIpInfo(context, request.getIpAddress(), null);
        }

        // Add phone info
        if (request.getPhoneNumber() != null) {
            context.withPhoneInfo(request.getPhoneNumber(), null, null);
        }

        // Add member info
        context = withMemberInfo(context, request.getMemberPoints(), request.getIsFrozenMember());

        // Assess risk
        var assessedContext = comprehensiveRiskAssessmentService.assessRisk(context);

        // Convert to response
        PhotoPointsRiskAssessResponse response = new PhotoPointsRiskAssessResponse();
        response.setMallCode(assessedContext.getMallCode().getCode());
        response.setIpAddress(assessedContext.getIpAddress());
        response.setPhoneNumber(assessedContext.getPhoneNumber());
        response.setRiskResult(assessedContext.getRiskResult());
        response.setBlockMessage(assessedContext.getBlockMessage());
        response.setAssessmentDetails(assessedContext.getAssessmentDetails());
        response.setAssessmentTime(java.time.LocalDateTime.now());

        if (assessedContext.getIpGeolocation() != null) {
            response.setIpGeolocation(convertIpGeolocation(assessedContext.getIpGeolocation()));
        }

        log.info("Photo points risk assessment complete - mall: {}, result: {}, details: {}",
                request.getMallCode(), response.getRiskResult(), response.getAssessmentDetails());

        return response;
    }

    private IpGeolocationInfo convertIpGeolocation(
            IpGeolocation geolocation) {
        return new IpGeolocationInfo(
                geolocation.getCountry(),
                geolocation.getCountryIsoCode(),
                geolocation.getSubdivision(),
                geolocation.getCity(),
                geolocation.isValid()
        );
    }
}
