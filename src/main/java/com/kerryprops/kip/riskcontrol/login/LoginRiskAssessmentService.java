package com.kerryprops.kip.riskcontrol.login;

import com.kerryprops.kip.riskcontrol.shared.IpGeolocation;
import com.kerryprops.kip.riskcontrol.shared.IpGeolocationInfo;
import com.kerryprops.kip.riskcontrol.shared.RiskAssessmentContext;
import com.kerryprops.kip.riskcontrol.shared.ComprehensiveRiskAssessmentService;
import com.kerryprops.kip.riskcontrol.shared.BaseRiskAssessmentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * Service for login scenario risk assessment.
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "drools.enabled", havingValue = "true", matchIfMissing = true)
public class LoginRiskAssessmentService extends BaseRiskAssessmentService {

    private final ComprehensiveRiskAssessmentService comprehensiveRiskAssessmentService;

    /**
     * Assess login risk.
     */
    public LoginRiskAssessResponse assessLoginRisk(LoginRiskAssessRequest request) {
        log.info("Starting login risk assessment - mall: {}, IP: {}, phone: {}",
                request.getMallCode(), request.getIpAddress(), request.getPhoneNumber());

        // Validate request
        validateCommonParameters(request.getMallCode());

        // Create context
        RiskAssessmentContext context = createBaseRiskContext(
                request.getMallCode(),
                RiskAssessmentContext.BusinessScenario.LOGIN
        );

        // Add user info
        context.setUnionId(request.getUnionId());

        // Add IP info
        if (request.getIpAddress() != null) {
            context = withIpInfo(context, request.getIpAddress(), null);
        }

        // Add member info
        context = withMemberInfo(context, request.getMemberPoints(), request.getIsFrozenMember());

        // Add phone info
        if (request.getPhoneNumber() != null) {
            context.withPhoneInfo(request.getPhoneNumber(), null, null);
        }

        // Add risk info
        context.withRiskInfo(
                request.getTxRiskLevel(),
                request.getUnionIdLoginCount(),
                request.getPhoneLoginCount()
        );

        // Assess risk
        var assessedContext = comprehensiveRiskAssessmentService.assessRisk(context);

        // Convert to response
        LoginRiskAssessResponse response = new LoginRiskAssessResponse();
        response.setMallCode(assessedContext.getMallCode().getCode());
        response.setUnionId(assessedContext.getUnionId());
        response.setIpAddress(assessedContext.getIpAddress());
        response.setRiskResult(assessedContext.getRiskResult());
        response.setBlockMessage(assessedContext.getBlockMessage());
        response.setAssessmentDetails(assessedContext.getAssessmentDetails());
        response.setAssessmentTime(java.time.LocalDateTime.now());

        if (assessedContext.getIpGeolocation() != null) {
            response.setIpGeolocation(convertIpGeolocation(assessedContext.getIpGeolocation()));
        }

        log.info("Login risk assessment complete - mall: {}, result: {}, details: {}",
                request.getMallCode(), response.getRiskResult(), response.getAssessmentDetails());

        return response;
    }

    private IpGeolocationInfo convertIpGeolocation(
            IpGeolocation geolocation) {
        return new IpGeolocationInfo(
                geolocation.getCountry(),
                geolocation.getCountryIsoCode(),
                geolocation.getSubdivision(),
                geolocation.getCity(),
                geolocation.isValid()
        );
    }
}
