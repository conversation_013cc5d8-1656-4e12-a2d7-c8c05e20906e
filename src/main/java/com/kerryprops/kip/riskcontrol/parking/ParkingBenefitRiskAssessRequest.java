package com.kerryprops.kip.riskcontrol.parking;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * Parking benefits risk assessment request.
 */
@Data
@Schema(description = "Parking benefits risk assessment request")
public class ParkingBenefitRiskAssessRequest {

    @NotBlank(message = "Mall code is required")
    @Schema(
            description = "Mall code",
            example = "JAKC",
            allowableValues = {"JAKC", "HKC", "KP", "ALL"},
            required = true
    )
    private String mallCode;

    @Schema(description = "IP address", example = "************")
    private String ipAddress;

    @Schema(description = "Member points", example = "1000")
    private Integer memberPoints;

    @Schema(description = "Is member frozen", example = "false")
    private Boolean isFrozenMember;
}
