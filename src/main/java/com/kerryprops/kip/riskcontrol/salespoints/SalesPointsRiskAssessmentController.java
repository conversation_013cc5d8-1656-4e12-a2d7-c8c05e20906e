package com.kerryprops.kip.riskcontrol.salespoints;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller for sales points risk assessment.
 */
@Slf4j
@RestController
@RequestMapping("/api/risk-assessment/sales-points")
@RequiredArgsConstructor
@ConditionalOnProperty(name = "drools.enabled", havingValue = "true", matchIfMissing = true)
@Tag(name = "Sales Points Risk Assessment", description = "Sales points risk assessment endpoints")
public class SalesPointsRiskAssessmentController {

    private final SalesPointsRiskAssessmentService salesPointsRiskAssessmentService;

    @Operation(
            summary = "Assess sales points risk",
            description = "Assess risk for sales points scenarios (automatic points/service desk points)"
    )
    @PostMapping
    public SalesPointsRiskAssessResponse assessSalesPointsRisk(@Valid @RequestBody SalesPointsRiskAssessRequest request) {
        log.info("Received sales points risk assessment request - mall: {}, points: {}",
                request.getMallCode(), request.getMemberPoints());

        SalesPointsRiskAssessResponse response = salesPointsRiskAssessmentService.assessSalesPointsRisk(request);

        log.info("Completed sales points risk assessment - mall: {}, result: {}",
                request.getMallCode(), response.getRiskResult());

        return response;
    }
}
