package com.kerryprops.kip.riskcontrol.salespoints;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * Sales points risk assessment request.
 */
@Data
@Schema(description = "Sales points risk assessment request")
public class SalesPointsRiskAssessRequest {

    @NotBlank(message = "Mall code is required")
    @Schema(
            description = "Mall code",
            example = "JAKC",
            allowableValues = {"JAKC", "HKC", "KP", "ALL"}
    )
    private String mallCode;

    @Schema(description = "Member points", example = "1000")
    private Integer memberPoints;

    @Schema(description = "Is member frozen", example = "false")
    private Boolean isFrozenMember;
}
