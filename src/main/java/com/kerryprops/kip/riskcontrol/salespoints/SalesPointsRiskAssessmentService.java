package com.kerryprops.kip.riskcontrol.salespoints;

import com.kerryprops.kip.riskcontrol.shared.BaseRiskAssessmentService;
import com.kerryprops.kip.riskcontrol.shared.ComprehensiveRiskAssessmentService;
import com.kerryprops.kip.riskcontrol.shared.RiskAssessmentContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * Service for sales points risk assessment.
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "drools.enabled", havingValue = "true", matchIfMissing = true)
public class SalesPointsRiskAssessmentService extends BaseRiskAssessmentService {

    private final ComprehensiveRiskAssessmentService comprehensiveRiskAssessmentService;

    /**
     * Assess sales points risk.
     */
    public SalesPointsRiskAssessResponse assessSalesPointsRisk(SalesPointsRiskAssessRequest request) {
        log.info("Starting sales points risk assessment - mall: {}, points: {}",
                request.getMallCode(), request.getMemberPoints());

        // Validate request
        validateCommonParameters(request.getMallCode());

        // Create context
        RiskAssessmentContext context = createBaseRiskContext(
                request.getMallCode(),
                RiskAssessmentContext.BusinessScenario.SALES_POINTS
        );

        // Add member info
        context = withMemberInfo(context, request.getMemberPoints(), request.getIsFrozenMember());

        // Assess risk
        var assessedContext = comprehensiveRiskAssessmentService.assessRisk(context);

        // Convert to response
        SalesPointsRiskAssessResponse response = new SalesPointsRiskAssessResponse();
        response.setMallCode(assessedContext.getMallCode().getCode());
        response.setMemberPoints(assessedContext.getMemberPoints());
        response.setRiskResult(assessedContext.getRiskResult());
        response.setBlockMessage(assessedContext.getBlockMessage());
        response.setAssessmentDetails(assessedContext.getAssessmentDetails());
        response.setAssessmentTime(java.time.LocalDateTime.now());

        log.info("Sales points risk assessment complete - mall: {}, result: {}, details: {}",
                request.getMallCode(), response.getRiskResult(), response.getAssessmentDetails());

        return response;
    }
}
