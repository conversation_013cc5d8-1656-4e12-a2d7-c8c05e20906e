package com.kerryprops.kip.riskcontrol.marketing;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * Marketing scenario risk assessment request.
 */
@Data
@Schema(description = "Marketing scenario risk assessment request")
public class MarketingRiskAssessRequest {

    @NotBlank(message = "Mall code is required")
    @Schema(
            description = "Mall code",
            example = "JAKC",
            allowableValues = {"JAKC", "HKC", "KP", "ALL"}
    )
    private String mallCode;

    @Schema(description = "Member points", example = "1000")
    private Integer memberPoints;

    @Schema(description = "Is member frozen", example = "false")
    private Boolean isFrozenMember;
}
