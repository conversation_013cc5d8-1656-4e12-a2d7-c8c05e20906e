package com.kerryprops.kip.riskcontrol.marketing;

import com.kerryprops.kip.riskcontrol.shared.RiskAssessmentContext;
import com.kerryprops.kip.riskcontrol.shared.BaseRiskAssessmentService;
import com.kerryprops.kip.riskcontrol.shared.ComprehensiveRiskAssessmentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * Service for marketing scenario risk assessment.
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "drools.enabled", havingValue = "true", matchIfMissing = true)
public class MarketingRiskAssessmentService extends BaseRiskAssessmentService {

    private final ComprehensiveRiskAssessmentService comprehensiveRiskAssessmentService;

    /**
     * Assess marketing scenario risk.
     */
    public MarketingRiskAssessResponse assessMarketingRisk(MarketingRiskAssessRequest request) {
        log.info("Starting marketing risk assessment - mall: {}, points: {}",
                request.getMallCode(), request.getMemberPoints());

        // Validate request
        validateCommonParameters(request.getMallCode());

        // Create context
        RiskAssessmentContext context = createBaseRiskContext(
                request.getMallCode(),
                RiskAssessmentContext.BusinessScenario.MARKETING
        );

        // Add member info
        context = withMemberInfo(context, request.getMemberPoints(), request.getIsFrozenMember());

        // Assess risk
        var assessedContext = comprehensiveRiskAssessmentService.assessRisk(context);

        // Convert to response
        MarketingRiskAssessResponse response = new MarketingRiskAssessResponse();
        response.setMallCode(assessedContext.getMallCode().getCode());
        response.setMemberPoints(assessedContext.getMemberPoints());
        response.setRiskResult(assessedContext.getRiskResult());
        response.setBlockMessage(assessedContext.getBlockMessage());
        response.setAssessmentDetails(assessedContext.getAssessmentDetails());
        response.setAssessmentTime(java.time.LocalDateTime.now());

        log.info("Marketing risk assessment complete - mall: {}, result: {}, details: {}",
                request.getMallCode(), response.getRiskResult(), response.getAssessmentDetails());

        return response;
    }
}
