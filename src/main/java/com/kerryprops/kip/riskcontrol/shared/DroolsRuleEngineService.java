package com.kerryprops.kip.riskcontrol.shared;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * Service for executing Drools rules engine operations.
 * Provides a functional interface for rule evaluation with proper resource management.
 *
 * <AUTHOR> Properties Limited
 * @since 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "drools.enabled", havingValue = "true", matchIfMissing = true)
public class DroolsRuleEngineService {

    private final KieContainer kieContainer;

    /**
     * Evaluates comprehensive risk assessment rules using Drools engine.
     * This method follows functional programming principles by being stateless
     * and managing KieSession lifecycle properly.
     *
     * @param context the context object containing all risk assessment data
     * @return the same context object with risk assessment results populated by rules
     */
    public RiskAssessmentContext evaluateRisk(RiskAssessmentContext context) {
        log.debug("Starting Drools rule evaluation for context: {}", context.getBusinessScenario());

        // Create a new KieSession for this evaluation (stateless approach)
        KieSession kieSession;
        try {
            kieSession = kieContainer.newKieSession();
        } catch (RuntimeException e) {
            log.error("Failed to create KieSession for IP: {}", context.getIpAddress(), e);
            throw e; // Re-throw the original exception
        }

        try {
            // Insert the context into working memory
            kieSession.insert(context);

            // Fire all applicable rules
            int rulesExecuted = kieSession.fireAllRules();

            log.info("Drools rule evaluation completed - Scenario: {}, Rules executed: {}, Result: {}",
                    context.getBusinessScenario(), rulesExecuted, context.getRiskResult());

            return context;

        } catch (RuntimeException e) {
            log.error("Error during Drools rule evaluation for context: {}", context.getBusinessScenario(), e);
            throw new RuntimeException("Failed to evaluate risk rules", e);
        } finally {
            // Always dispose of the KieSession to free resources
            kieSession.dispose();
            log.debug("KieSession disposed for context: {}", context.getBusinessScenario());
        }
    }

    /**
     * Checks if the Drools rule engine is properly configured and available.
     *
     * @return true if the rule engine is available, false otherwise
     */
    public boolean isRuleEngineAvailable() {
        try {
            KieSession testSession = kieContainer.newKieSession();
            testSession.dispose();
            return true;
        } catch (RuntimeException e) {
            log.warn("Drools rule engine is not available", e);
            return false;
        }
    }
}
