package com.kerryprops.kip.common.jpa;

import com.querydsl.jpa.impl.JPAQuery;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.lang.Nullable;

import java.util.Collections;
import java.util.List;

/**
 * jpa查询参数处理工具类.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> 2024-06-24 17:20:44
 **/
public final class JpaQueryUtil {

    private JpaQueryUtil() {
    }

    /**
     * 对模糊查询参数做添加 '%%'处理.
     *
     * @param param 查询参数
     * @return 处理结果
     */
    public static @Nullable String fuzzyParam(@Nullable String param) {
        if (StringUtils.isEmpty(param)) {
            return param;
        }
        return "%" + param + "%";
    }

    /**
     * 把需要更新的属性[非null且和数据库实体属性不一样]，赋予数据库表实体.
     *
     * @param <T>         实体类型
     * @param requestBody 查询参数
     * @param entityInDb  数据库表实体
     */
    @SuppressWarnings("unchecked")
    public static <T> void setIfDiff(T requestBody, T entityInDb) {
        BeanMap requestMap = BeanMap.create(requestBody);
        BeanMap entityInDbMap = BeanMap.create(entityInDb);
        requestMap.forEach((k, v) -> {
            if (v != null && entityInDbMap.get(k) != v) {
                entityInDbMap.put(k, v);
            }
        });
    }

    @SuppressWarnings("unchecked")
    public static <T> void setFieldsNull(T requestBody) {
        BeanMap requestMap = BeanMap.create(requestBody);
        requestMap.forEach((k, v) -> {
            if (v != null) {
                requestMap.put(k, null);
            }
        });
    }

    @SuppressWarnings("deprecation")
    public static <T> Page<T> pageQuery(JPAQuery<T> jpaQuery, Pageable pageable) {
        long count = jpaQuery.fetchCount();
        if (count <= 0L) {
            return new PageImpl<>(Collections.emptyList(), pageable, 0L);
        }
        List<T> vos = jpaQuery.offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .fetch();
        return new PageImpl<>(vos, pageable, count);
    }

}
