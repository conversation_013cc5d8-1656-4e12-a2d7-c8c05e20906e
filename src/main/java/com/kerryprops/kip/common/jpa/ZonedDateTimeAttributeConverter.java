package com.kerryprops.kip.common.jpa;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.springframework.lang.Nullable;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

/**
 * ZonedDateTime属性转换器.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> zhang
 */
@Converter(autoApply = true)
final class ZonedDateTimeAttributeConverter implements AttributeConverter<ZonedDateTime, String> {

    private static final String LOCAL_DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    private static final DateTimeFormatter LOCAL_DATE_TIME_FORMATTER
            = DateTimeFormatter.ofPattern(LOCAL_DATE_TIME_PATTERN)
            .withZone(ZoneId.systemDefault());

    @Override
    public @Nullable String convertToDatabaseColumn(@Nullable ZonedDateTime attribute) {
        if (attribute == null) {
            return null;
        }
        return attribute.format(LOCAL_DATE_TIME_FORMATTER);
    }

    @Override
    public @Nullable ZonedDateTime convertToEntityAttribute(@Nullable String dbData) {
        if (dbData == null) {
            return null;
        }
        return LocalDateTime.parse(dbData, LOCAL_DATE_TIME_FORMATTER).atZone(ZoneId.systemDefault());
    }

}
