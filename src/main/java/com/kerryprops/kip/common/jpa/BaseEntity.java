package com.kerryprops.kip.common.jpa;

import jakarta.persistence.Column;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Generated;
import org.hibernate.generator.EventType;
import org.springframework.lang.Nullable;

import java.time.ZonedDateTime;


/**
 * 数据库实体基类，所有数据库实体都推荐继承这个类.
 *
 * <AUTHOR> 2024-06-21 17:32:27
 */
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@MappedSuperclass
@ToString
public abstract class BaseEntity {

    @Id
    @Column(name = "id")
    @Nullable
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 创建时间.
     */
    @ColumnDefault("CURRENT_TIMESTAMP")
    @Column(name = "created_time", nullable = false)
    @Generated(event = EventType.INSERT)
    private ZonedDateTime createdTime;

    /**
     * 更新时间.
     */
    @ColumnDefault("CURRENT_TIMESTAMP")
    @Column(name = "updated_time", nullable = false)
    @Generated(event = EventType.UPDATE)
    private ZonedDateTime updatedTime;

}
