package com.kerryprops.kip.common.jpa;

import com.google.common.base.Objects;
import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.Visitor;
import org.springframework.lang.Nullable;

import java.util.Collection;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * 参考 BooleanBuilder 实现的 queryDsl 查询参数拼接器.
 * 主要拓展功能：
 * 1、 参数非空 则拼接 Predicate 的方法
 * 2、 condition 为true , 则拼接 Predicate 的方法
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> 2023-10-11 15:14:30
 **/
public class WhereBuilder implements Predicate {

    @Nullable
    private Predicate predicate;

    public WhereBuilder() {
    }

    public WhereBuilder(Predicate initial) {
        predicate = (Predicate) ExpressionUtils.extract(initial);
    }

    @Override
    public @Nullable <R, C> R accept(@Nullable Visitor<R, C> v, @Nullable C context) {
        if (predicate != null) {
            return predicate.accept(v, context);
        } else {
            return null;
        }
    }

    @Override
    public Class<? extends Boolean> getType() {
        return Boolean.class;
    }

    public WhereBuilder and(@Nullable Predicate right) {
        if (right != null) {
            if (predicate == null) {
                predicate = right;
            } else {
                predicate = ExpressionUtils.and(predicate, right);
            }
        }
        return this;
    }

    /**
     * 如果 obj 不为空， 则拼接 and 查询条件.
     *
     * @param <T>   泛型
     * @param obj   查询参数
     * @param right 表达式
     * @return whereBuilder
     */
    public <T> WhereBuilder and(@Nullable T obj, Function<T, Predicate> right) {
        if (isEmpty(obj)) {
            return this;
        }
        return and(right.apply(obj));
    }

    public WhereBuilder and(boolean condition, Supplier<Predicate> right) {
        if (!condition) {
            return this;
        }
        return and(right.get());
    }

    /**
     * 如果 args 不为空， 则拼接 ( or 查询条件1 or 查询条件2 or 查询条件3...).
     *
     * @param args union of predicates
     */
    public void andAnyOf(Predicate... args) {
        if (args.length > 0) {
            and(ExpressionUtils.anyOf(args));
        }
    }

    /**
     * Create the insertion of this and the negation of the given predicate.
     *
     * @param right predicate to be negated
     */
    public void andNot(Predicate right) {
        and(right.not());
    }

    /**
     * 如果 obj 不为空， 则拼接 and !查询条件.
     *
     * @param <T>   泛型
     * @param obj   查询参数
     * @param right 表达式
     */
    public <T> void andNot(@Nullable T obj, Function<T, Predicate> right) {
        if (isEmpty(obj)) {
            return;
        }
        andNot(right.apply(obj));
    }

    public void andNot(boolean condition, Supplier<Predicate> right) {
        if (!condition) {
            return;
        }
        andNot(right.get());
    }

    @Nullable
    public Predicate getValue() {
        return predicate;
    }

    public boolean hasValue() {
        return predicate != null;
    }

    @Override
    public int hashCode() {
        return predicate != null ? predicate.hashCode() : 0;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (o instanceof WhereBuilder builder) {
            return Objects.equal(builder.getValue(), predicate);
        } else {
            return false;
        }
    }

    @Override
    public String toString() {
        return predicate != null ? predicate.toString() : super.toString();
    }

    @Override
    public WhereBuilder not() {
        if (predicate != null) {
            predicate = predicate.not();
        }
        return this;
    }

    public void or(@Nullable Predicate right) {
        if (right != null) {
            if (predicate == null) {
                predicate = right;
            } else {
                predicate = ExpressionUtils.or(predicate, right);
            }
        }
    }

    /**
     * 如果 obj 不为空， 则拼接 or 查询条件.
     *
     * @param <T>   泛型
     * @param obj   查询参数
     * @param right 表达式
     */
    public <T> void or(@Nullable T obj, Function<T, Predicate> right) {
        if (isEmpty(obj)) {
            return;
        }
        or(right.apply(obj));
    }

    public void or(boolean condition, Supplier<Predicate> right) {
        if (!condition) {
            return;
        }
        or(right.get());
    }

    /**
     * 如果 args 不为空， 则拼接  or (  查询条件1 and 查询条件2 and 查询条件3...).
     *
     * @param args intersection of predicates
     */
    public void orAllOf(Predicate... args) {
        if (args.length > 0) {
            or(ExpressionUtils.allOf(args));
        }
    }

    public void orNot(Predicate right) {
        or(right.not());
    }

    public <T> void orNot(@Nullable T obj, Function<T, Predicate> right) {
        if (isEmpty(obj)) {
            return;
        }
        orNot(right.apply(obj));
    }

    public void orNot(boolean condition, Supplier<Predicate> right) {
        if (!condition) {
            return;
        }
        orNot(right.get());
    }

    private boolean isEmpty(@Nullable Object obj) {
        if (obj == null) {
            return true;
        }
        if (obj instanceof Collection<?> c) {
            return c.isEmpty();
        }
        return false;
    }

}
