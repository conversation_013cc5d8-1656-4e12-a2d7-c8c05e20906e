package com.kerryprops.kip.common.jpa;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.springframework.lang.Nullable;

import java.time.LocalDateTime;

/**
 * LocalDateTime秒级精度转换器
 * 在保存到数据库时截断毫秒/纳秒信息
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> <PERSON>
 * Created Date - 2024/7/16 下午3:10
 */
@Converter(autoApply = true)
public class LocalDateTimeSecondsConverter implements AttributeConverter<LocalDateTime, LocalDateTime> {

    @Override
    public @Nullable LocalDateTime convertToDatabaseColumn(@Nullable LocalDateTime attribute) {
        if (attribute == null) {
            return null;
        }
        return attribute.withNano(0);
    }

    @Override
    public LocalDateTime convertToEntityAttribute(LocalDateTime dbData) {
        return dbData;
    }

}