package com.kerryprops.kip.common.jpa;

import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import org.springframework.data.jpa.repository.support.JpaEntityInformation;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.util.function.SingletonSupplier;

/**
 * JpaAdaptor实现类.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * @param <T> 实体类
 * <AUTHOR> Yu
 */
public class JpaAdaptor<T extends BaseEntity> extends SimpleJpaRepository<T, Long> implements BaseJpaAdaptor<T> {

    private final EntityManager entityManager;

    private final SingletonSupplier<JPAQueryFactory> jpaQueryFactory;

    public JpaAdaptor(JpaEntityInformation<T, ?> entityInformation,
                      EntityManager entityManager) {

        super(entityInformation, entityManager);
        this.entityManager = entityManager;
        jpaQueryFactory = SingletonSupplier.of(() -> new JPAQueryFactory(this.entityManager));
    }

    @Override
    public JPAQueryFactory getQuery() {
        var queryFactory = jpaQueryFactory.get();
        assert queryFactory != null;
        return queryFactory;
    }

}
