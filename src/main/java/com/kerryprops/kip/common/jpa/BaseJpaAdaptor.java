package com.kerryprops.kip.common.jpa;

import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.NoRepositoryBean;

/**
 * 基础JPA适配接口，提供通用的数据访问功能.
 *
 * @param <T> 实体类型
 * <AUTHOR> 2023-05-22 20:02:26
 */
@NoRepositoryBean
interface BaseJpaAdaptor<T> extends JpaRepository<T, Long> {

    /**
     * 获取 JPAQueryFactory.
     *
     * @return jPAQueryFactory
     */
    JPAQueryFactory getQuery();

}
