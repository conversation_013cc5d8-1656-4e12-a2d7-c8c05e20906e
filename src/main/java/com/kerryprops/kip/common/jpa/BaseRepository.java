package com.kerryprops.kip.common.jpa;

import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.NoRepositoryBean;

/**
 * BaseRepository.
 * 1、用于数据库单表增删查改
 * 2、避免每个 Repository 都要拓展QuerydslPredicateExecutor、JpaRepository
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * @param <T> 实体类型
 * <AUTHOR> 2023-05-23 21:49:26
 **/
@NoRepositoryBean
public interface BaseRepository<T> extends BaseJpaAdaptor<T>, QuerydslPredicateExecutor<T> {

}
