package com.kerryprops.kip.common;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * 用于管理 MDC（映射诊断上下文）日志上下文的过滤器.
 *
 * <p>
 * 该过滤器确保为每个传入请求正确设置 MDC 上下文中的关联 ID，并在请求处理后将其移除.
 * 它以最高优先级运行，以确保在整个请求生命周期中关联 ID 可用.
 *
 * <AUTHOR> Properties Limited
 * @see OncePerRequestFilter
 * @see MdcLoggingUtil
 * @since 1.0
 */
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class MdcLoggingFilter extends OncePerRequestFilter {

    /**
     * 处理每个请求以在 MDC 上下文中设置和清除关联 ID.
     * 该方法由 servlet 容器为每个传入请求调用.
     * 它为请求和响应设置关联 ID，调用过滤器链中的下一个过滤器，然后在请求处理后清除关联 ID.
     *
     * @param request     the HTTP servlet request
     * @param response    the HTTP servlet response
     * @param filterChain the filter chain to continue processing
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     * @see MdcLoggingUtil#extractAndSetCorrelationIdFromRequest(HttpServletRequest)
     * @see MdcLoggingUtil#propagateCorrelationIdToResponse(HttpServletResponse)
     * @see MdcLoggingUtil#clearCorrelationIdFromContext()
     */
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        MdcLoggingUtil.extractAndSetCorrelationIdFromRequest(request);
        MdcLoggingUtil.propagateCorrelationIdToResponse(response);
        filterChain.doFilter(request, response);
        MdcLoggingUtil.clearCorrelationIdFromContext();
    }

}
