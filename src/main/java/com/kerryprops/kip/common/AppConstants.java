package com.kerryprops.kip.common;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * 服务应用程序的集中常量.
 *
 * <p>
 * 该类包含整个应用程序中使用的各种常量，包括日期/时间格式、关联 ID 键和应用程序特定的前缀.
 *
 * <p>
 * 此类中定义的常量用于在整个应用程序中保持一致性和可读性.它们用于各种组件，例如日志记录、数据格式化和标识符生成.
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@UtilityClass
public final class AppConstants {

    /**
     * 用于格式化和解析本地日期/时间的日期-时间模式.
     *
     * <p>
     * 模式： "yyyy-MM-dd HH:mm:ss"
     *
     * <p>
     * 此模式用于格式化和解析应用程序中的日期/时间值.它与 {@link #LOCAL_DATE_TIME_FORMATTER} 一起使用，以确保整个应用程序中日期/时间格式的一致性.
     */
    public static final String LOCAL_DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    /**
     * 配置为使用 LOCAL_DATE_TIME_PATTERN 的 DateTimeFormatter 实例，并设置为 Asia/Shanghai 时区.
     *
     * <p>
     * 此格式化程序用于格式化和解析应用程序中的日期/时间值.它配置为使用 {@link #LOCAL_DATE_TIME_PATTERN} 并设置为 Asia/Shanghai 时区，以确保整个应用程序中日期/时间格式的一致性.
     */
    public static final DateTimeFormatter LOCAL_DATE_TIME_FORMATTER
            = DateTimeFormatter.ofPattern(LOCAL_DATE_TIME_PATTERN).withZone(ZoneId.of("Asia/Shanghai"));

    /**
     * 在 MDC 上下文中存储和检索关联 ID 的键.
     *
     * <p>
     * 此键用于在 MDC 上下文中存储和检索关联 ID.关联 ID 用于跟踪整个应用程序的请求和响应，并用于日志记录和调试目的.
     */
    public static final String MDC_CORRELATION_ID_KEY = "X-Correlation-ID";

    /**
     * 用于生成唯一标识符的应用程序前缀.
     *
     * <p>
     * 此前缀用于生成整个应用程序的唯一标识符.它可以与其他值（如时间戳和随机数）一起使用，以确保生成的标识符是唯一的和一致的.
     */
    public static final String APP_PREFIX = "KIT";

    /**
     * 连字符常量，用于整个应用程序的一致使用.
     *
     * <p>
     * 此常量用于表示整个应用程序中的连字符.它用于确保在各种组件中（如日志记录和数据格式化）一致地使用连字符.
     */
    public static final String HYPHEN = "-";

}
