package com.kerryprops.kip.common;

import io.swagger.v3.oas.annotations.Hidden;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.health.HealthContributorRegistry;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.boot.actuate.health.Status;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * kerry-pay-service
 *
 * <p>
 * 该类用于处理健康检查请求。
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Sam <PERSON>
 * Created Date - 2022/11/1 10:14
 */
@Slf4j
@Hidden
@RequestMapping("/health")
@RestController
@RequiredArgsConstructor
public class HealthController {

    private final HealthContributorRegistry healthRegistry;

    @GetMapping(value = "/shallow",
            produces = "application/json") 
    public Object healthProxy() {
        try {
            var dbHealthIndicator = (HealthIndicator) healthRegistry.getContributor("db");
            var redisHealthIndicator = (HealthIndicator) healthRegistry.getContributor("redis");
            return Stream.of(dbHealthIndicator.health().getStatus(), redisHealthIndicator.health().getStatus())
                    .allMatch(status -> Objects.equals(Status.UP, status)) ? 
                    HealthDto.SUCCESS_RESPONSE : HealthDto.FAIL_RESPONSE;
        } catch (Exception e) {
            log.error("Health shallow check failed: ", e);
            return HealthDto.FAIL_RESPONSE;
        }
    }

    record HealthDto(int health) {

        static final HealthDto SUCCESS_RESPONSE = new HealthDto(0);

        static final HealthDto FAIL_RESPONSE = new HealthDto(Integer.MIN_VALUE);

    }

}
