package com.kerryprops.kip.common;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Jackson JSON处理设置的配置类.
 *
 * <p>
 * 该类提供了一个主ObjectMapper bean，配置了用于JSON序列化和反序列化的自定义设置.
 *
 * <AUTHOR>
 * @since 1.0
 */
@Configuration(proxyBeanMethods = false)
public class Jackson2JsonConfig {

    /**
     * 创建并配置主ObjectMapper bean.
     *
     * <p>
     * 该bean使用JsonUtils.JACKSON中的预定义设置，以确保应用程序中JSON处理的一致性.
     *
     * @return 配置好的ObjectMapper实例
     */
    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        return JsonUtils.JACKSON.getObjectMapper();
    }

}
