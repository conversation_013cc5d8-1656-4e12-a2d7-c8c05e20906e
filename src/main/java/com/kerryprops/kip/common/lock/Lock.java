package com.kerryprops.kip.common.lock;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 该注解应用于方法.当调用带有此注解的方法时，它将尝试获取分布式锁.
 * 如果成功获取锁，方法将执行并在完成后释放锁.
 * 如果获取锁失败，根据`discardRepeatRequestQuietly`设置，它将抛出异常或静默失败.
 *
 * <p>
 * 锁的键由`keys`属性指定，可以是一个SpEL表达式.表达式的评估上下文是方法的参数.
 * 锁的持续时间由`duration`属性指定，默认为3秒.
 * 如果`blockRepeatCommit`为true，则在锁的持续时间内，具有相同键的请求将被阻塞，直到锁被释放.
 * 如果`discardRepeatRequestQuietly`为true，锁获取失败将静默失败而不抛出异常.
 *
 * <p>
 * (c) Kerry Properties Limited. All rights reserved.
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface Lock {

    String keys();

    String duration() default "PT3s";

    boolean blockRepeatCommit() default false;

    boolean discardRepeatRequestQuietly() default false;

}
