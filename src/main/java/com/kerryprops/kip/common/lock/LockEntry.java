package com.kerryprops.kip.common.lock;

import com.kerryprops.kip.common.AppConstants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Duration;
import java.util.List;

/**
 * 该类封装了与分布式锁相关的信息.
 *
 * <p>
 * 它包括锁的键、持续时间以及成功或失败时的行为等详细信息.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR>
 * @since 2023/02/21
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LockEntry {

    private static final String PREFIX = AppConstants.APP_PREFIX + AppConstants.HYPHEN + "lock";

    /**
     * 锁的键列表，用于唯一标识锁.
     */
    private List<String> keys;

    /**
     * 锁的持有时间.
     */
    private Duration duration;

    /**
     * 是否在成功后自动释放锁.
     */
    private boolean releaseAfterSuccess;

    /**
     * 是否静默丢弃重复请求.
     */
    private boolean discardRepeatRequestQuietly;

    public List<String> getKeys() {
        return keys.stream().map(it -> String.format("%s:%s", PREFIX, it)).toList();
    }

}
