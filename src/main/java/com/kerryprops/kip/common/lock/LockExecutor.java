package com.kerryprops.kip.common.lock;

import com.kerryprops.kip.exception.LockAlreadyOccupiedException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 执行带有分布式锁的操作的类.
 *
 * <p>
 * 该类使用Redisson来管理分布式锁，确保对共享资源的线程安全和一致访问.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR>
 * @since 2023/02/21
 */
@Slf4j
@Component
@ConditionalOnClass(Redisson.class)
@RequiredArgsConstructor
public class LockExecutor {

    private final RedissonClient redissonClient;

    /**
     * 在分布式锁的保护下执行给定的任务.
     *
     * @param lockEntry 锁的相关信息
     * @param supplier  要在锁保护下执行的任务
     * @param <T>       任务返回值的类型
     * @return 任务执行的结果
     * @throws InterruptedException 如果线程被中断
     */
    public @Nullable <T> T executeWithLock(LockEntry lockEntry, Supplier<T> supplier) throws InterruptedException {
        var lock = toLock(lockEntry.getKeys());
        if (!acquireLock(lock, lockEntry)) {
            return null;
        }
        var withException = false;
        try {
            return supplier.get();
        } catch (Exception e) {
            withException = true;
            throw e;
        } finally {
            releaseLock(lock, lockEntry, withException);
        }
    }

    RLock toLock(List<String> keys) {
        Assert.notEmpty(keys, "Keys cannot be empty");

        if (keys.size() == 1) {
            return redissonClient.getLock(keys.get(0));
        }
        return redissonClient.getMultiLock(
                keys.stream().map(redissonClient::getLock).toArray(RLock[]::new));
    }

    private boolean acquireLock(RLock lock, LockEntry lockEntry) throws InterruptedException {
        var isAcquired = lock.tryLock(-1, lockEntry.getDuration().toMillis(), TimeUnit.MILLISECONDS);
        if (!isAcquired) {
            if (lockEntry.isDiscardRepeatRequestQuietly()) {
                log.error("Acquire lock failed: {}, discard current request", lockEntry.getKeys());
                return false;
            }
            throw LockAlreadyOccupiedException.lockAlreadyOccupiedException(lockEntry.getKeys()).get();
        }
        return true;
    }

    private void releaseLock(RLock lock, LockEntry lockEntry, boolean withException) {
        if ((lockEntry.isReleaseAfterSuccess() || withException) && lock.isLocked()) {
            log.debug("Release lock: {}", lockEntry.getKeys());
            lock.unlock();
        }
    }

}
