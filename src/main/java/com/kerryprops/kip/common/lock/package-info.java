/**
 * 提供方法和资源的分布式锁功能.
 *
 * <p>
 * 该包包含用于实现分布式锁的注解和工具，确保跨分布式系统对共享资源的线程安全和一致访问.
 * 主要组件包括：
 * <ul>
 *   <li>{@link Lock}: 用于对方法应用分布式锁的注解.</li>
 *   <li>{@link LockAspect}: 处理锁获取和释放的切面.</li>
 *   <li>{@link LockExecutor}: 管理锁操作的执行器.</li>
 * </ul>
 *
 * <p>
 * (c) Kerry Properties Limited. 保留所有权利.
 */
@NonNullApi
@NonNullFields
package com.kerryprops.kip.common.lock;

import org.springframework.lang.NonNullApi;
import org.springframework.lang.NonNullFields;
