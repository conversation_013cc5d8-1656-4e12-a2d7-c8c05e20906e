package com.kerryprops.kip.common.lock;

import com.google.common.base.Throwables;
import com.kerryprops.kip.exception.UnknownException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.core.Ordered;
import org.springframework.core.StandardReflectionParameterNameDiscoverer;
import org.springframework.core.annotation.Order;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 该切面处理@Lock注解以获取和管理分布式锁.
 *
 * <p>
 * 通过管理锁的获取和释放，确保对共享资源的线程安全和一致访问.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zhang
 * @since 2023/02/21
 */
@Aspect
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
@ConditionalOnBean(LockExecutor.class)
public class LockAspect {

    private final LockExecutor lockExecutor;

    private final ExpressionParser parser = new SpelExpressionParser();

    public LockAspect(LockExecutor lockExecutor) {
        this.lockExecutor = lockExecutor;
    }

    @Around("@annotation(com.kerryprops.kip.common.lock.Lock)")
    public @Nullable Object lockAdvice(ProceedingJoinPoint jp) throws InterruptedException {
        LockEntry lockEntry = parse(jp);

        return lockExecutor.executeWithLock(lockEntry, () -> {
            try {
                return jp.proceed();
            } catch (Throwable throwable) {
                Throwables.throwIfUnchecked(throwable);
                throw new UnknownException(throwable);
            }
        });
    }

    private LockEntry parse(ProceedingJoinPoint point) {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();

        Lock lockAnnotation = method.getDeclaredAnnotation(Lock.class);
        EvaluationContext context = VariableEvaluationContextUtil.processMethodExpression(point);

        String value = Optional.ofNullable(parser.parseExpression(lockAnnotation.keys())
                .getValue(context, String.class)).orElse("");

        List<String> keys = Arrays.stream(value.split(",")).toList();
        return LockEntry.builder().keys(keys).duration(Duration.parse(lockAnnotation.duration()))
                .releaseAfterSuccess(!lockAnnotation.blockRepeatCommit()).discardRepeatRequestQuietly(lockAnnotation
                        .discardRepeatRequestQuietly())
                .build();
    }

    /**
     * 用于处理方法表达式中的变量.
     */
    private static final class VariableEvaluationContextUtil {

        private static final StandardReflectionParameterNameDiscoverer DISCOVERER =
                new StandardReflectionParameterNameDiscoverer();

        private static EvaluationContext processMethodExpression(ProceedingJoinPoint point) {
            MethodSignature signature = (MethodSignature) point.getSignature();
            Method method = signature.getMethod();
            Object[] argValues = point.getArgs();
            String[] paraNames = DISCOVERER.getParameterNames(method);
            EvaluationContext context = new StandardEvaluationContext();
            if (Objects.nonNull(paraNames) && Objects.nonNull(argValues) && paraNames.length == argValues.length) {
                for (int i = 0; i < paraNames.length; i++) {
                    context.setVariable(paraNames[i], argValues[i]);
                }
            }
            return context;
        }

    }

}
