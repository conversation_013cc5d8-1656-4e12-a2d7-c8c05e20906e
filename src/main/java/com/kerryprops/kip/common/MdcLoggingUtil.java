package com.kerryprops.kip.common;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.lang.Nullable;

import java.util.UUID;

/**
 * 用于管理 MDC（映射诊断上下文）日志上下文的工具类.
 *
 * <p>
 * 该类提供了初始化、检索和移除 MDC 上下文中关联 ID 的方法.
 * 它还处理在 HTTP 请求和响应头中设置关联 ID.
 *
 * <AUTHOR> Properties Limited
 * @since 1.0
 */
public final class MdcLoggingUtil {

    private static final String CORRELATION_ID_HEADER_NAME = "X-Correlation-ID";

    private static final int GENERATED_UNIQUE_ID_SIZE_LIMIT = 20;

    private static final int CHARS_TO_REMOVE = GENERATED_UNIQUE_ID_SIZE_LIMIT - AppConstants.APP_PREFIX.length();

    private MdcLoggingUtil() {
    }

    /**
     * 初始化 MDC 上下文中的关联 ID.
     * 如果没有提供关联 ID，则会生成一个新的 ID.
     */
    public static void initializeCorrelationIdWithValue() {
        initCorrelationId(null);
    }

    /**
     * 初始化 MDC 上下文中的关联 ID.
     *
     * @param correlationId 要初始化的关联 ID（可以为 null）
     */
    public static void initCorrelationId(@Nullable String correlationId) {
        if (StringUtils.isBlank(correlationId)) {
            correlationId = generateCorrelationId();
        }
        MDC.put(AppConstants.MDC_CORRELATION_ID_KEY, correlationId);
    }

    /**
     * 检索 MDC 上下文中的当前关联 ID.
     *
     * @return 当前关联 ID
     */
    public static String retrieveCurrentCorrelationId() {
        return MDC.get(AppConstants.MDC_CORRELATION_ID_KEY);
    }

    /**
     * 从 MDC 上下文中移除关联 ID.
     */
    public static void clearCorrelationIdFromContext() {
        MDC.remove(AppConstants.MDC_CORRELATION_ID_KEY);
    }

    /**
     * 根据 HTTP 请求头设置 MDC 上下文中的关联 ID.
     *
     * @param request HTTP servlet 请求
     */
    public static void extractAndSetCorrelationIdFromRequest(HttpServletRequest request) {
        var correlationId = request.getHeader(CORRELATION_ID_HEADER_NAME);
        initCorrelationId(correlationId);
    }

    /**
     * 将关联 ID 设置到 HTTP 响应头中.
     *
     * @param response HTTP servlet 响应
     */
    public static void propagateCorrelationIdToResponse(HttpServletResponse response) {
        String correlationId = retrieveCurrentCorrelationId();
        if (StringUtils.isNotBlank(correlationId)) {
            response.setHeader(CORRELATION_ID_HEADER_NAME, correlationId);
        }
    }

    /**
     * 生成一个唯一的 ID 用于关联 ID.
     *
     * @return 唯一的关联 ID
     */
    private static String generateCorrelationId() {
        var uuid = UUID.randomUUID().toString();
        uuid = StringUtils.right(uuid, CHARS_TO_REMOVE);
        return AppConstants.APP_PREFIX + AppConstants.HYPHEN + uuid;
    }

}
