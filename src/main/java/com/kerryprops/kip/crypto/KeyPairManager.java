package com.kerryprops.kip.crypto;

import lombok.Getter;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;

import java.io.FileNotFoundException;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.security.GeneralSecurityException;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * KeyPairManager.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Yu 2025-04-08 09:44:51
 **/
@Getter
@Component
public class KeyPairManager {

    private static final String ALGORITHM = "RSA";

    private final ResourceLoader resourceLoader;

    private final PrivateKey privateKey;

    private final PublicKey publicKey;

    public KeyPairManager(ResourceLoader resourceLoader, RsaProperties rsaProperties)
            throws GeneralSecurityException, IOException {

        this.resourceLoader = resourceLoader;
        privateKey = loadPrivateKey(rsaProperties.getPrivateKeyPath());
        publicKey = loadPublicKey(rsaProperties.getPublicKeyPath());
    }

    /**
     * 生成RSA密钥对，并将生成的公钥和私钥以Base64编码的形式保存到指定文件路径中.
     *
     * @param publicKeyPath  公钥保存的文件路径
     * @param privateKeyPath 私钥保存的文件路径
     * @throws GeneralSecurityException 如果在生成密钥对的过程中发生加密相关的错误
     * @throws IOException              如果在保存密钥到文件时发生I/O错误
     */
    public static void generateKeyPair(String publicKeyPath, String privateKeyPath)
            throws GeneralSecurityException, IOException {

        // 创建密钥对生成器
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(ALGORITHM);
        // 初始化密钥长度
        keyPairGenerator.initialize(2048);
        // 生成密钥对
        KeyPair keyPair = keyPairGenerator.generateKeyPair();

        // 获取公钥和私钥
        PublicKey publicKey = keyPair.getPublic();
        PrivateKey privateKey = keyPair.getPrivate();

        // 将密钥转换为Base64编码
        String publicKeyBase64 = Base64.getEncoder().encodeToString(publicKey.getEncoded());
        String privateKeyBase64 = Base64.getEncoder().encodeToString(privateKey.getEncoded());

        // 保存到文件
        saveToFile(publicKeyPath, publicKeyBase64);
        saveToFile(privateKeyPath, privateKeyBase64);
    }

    private static void saveToFile(String filePath, String content) throws IOException {
        try (FileWriter fileWriter = new FileWriter(filePath)) {
            fileWriter.write(content);
        }
    }

    private PrivateKey loadPrivateKey(String path) throws IOException, GeneralSecurityException {
        // 实现从文件加载私钥的逻辑
        byte[] keyBytes = loadKeyBytesFromClasspath(path);
        PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory kf = KeyFactory.getInstance(ALGORITHM);
        return kf.generatePrivate(spec);
    }

    private PublicKey loadPublicKey(String path) throws IOException, GeneralSecurityException {
        // 实现从文件加载公钥的逻辑
        byte[] keyBytes = loadKeyBytesFromClasspath(path);
        X509EncodedKeySpec spec = new X509EncodedKeySpec(keyBytes);
        KeyFactory kf = KeyFactory.getInstance(ALGORITHM);
        return kf.generatePublic(spec);
    }

    private byte[] loadKeyBytesFromClasspath(String path) throws IOException {
        String classpathLocation = "classpath:" + path;
        Resource resource = resourceLoader.getResource(classpathLocation);

        if (!resource.exists()) {
            throw new FileNotFoundException("在classpath中未找到密钥文件: " + path);
        }

        try (InputStream inputStream = resource.getInputStream()) {
            byte[] keyBytes = inputStream.readAllBytes();
            return Base64.getDecoder().decode(keyBytes);
        }
    }

}
