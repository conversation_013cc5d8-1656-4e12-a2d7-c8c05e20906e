package com.kerryprops.kip.crypto;

import com.kerryprops.kip.exception.DecryptException;
import com.kerryprops.kip.exception.EncryptException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.ObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.security.Key;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.Base64;

/**
 * RSA加解密服务.
 */
@Slf4j
@Service
@SuppressWarnings("IllegalCatch")
public class RsaCryptoService {

    private final PrivateKey privateKey;

    private final PublicKey publicKey;

    private final String base64PublicKey;

    private final ObjectPool<Cipher> decryptCipherPool;

    private final ObjectPool<Cipher> encryptCipherPool;

    public RsaCryptoService(RsaProperties rsaProperties, KeyPairManager keyLoader)
            throws GeneralSecurityException, IOException {
        
        privateKey = keyLoader.getPrivateKey();
        publicKey = keyLoader.getPublicKey();

        byte[] encoded = publicKey.getEncoded();
        base64PublicKey = Base64.getEncoder().encodeToString(encoded);
        log.info("RSA KeyPair initialized. (Demo: generated on the fly)");

        decryptCipherPool = createCipherPool(rsaProperties, privateKey, Cipher.DECRYPT_MODE);
        encryptCipherPool = createCipherPool(rsaProperties, publicKey, Cipher.ENCRYPT_MODE);
    }

    /**
     * 解密提供的Base64编码的加密字符串.
     *
     * @param base64Encrypted 使用Base64编码的加密字符串
     * @return 解密后的字符串，使用UTF-8编码
     * @throws DecryptException 当解密过程出现错误时抛出，包括RSA解密失败、IO操作失败或其他运行时错误
     */
    public String decrypt(String base64Encrypted) throws DecryptException {
        if (base64Encrypted == null) {
            throw new DecryptException("Encrypted text cannot be null");
        }
        
        Cipher cipher = null;
        try {
            byte[] encryptedBytes = Base64.getDecoder().decode(base64Encrypted);

            cipher = borrowCipherFromPool(decryptCipherPool, "decryption");

            cipher.init(Cipher.DECRYPT_MODE, privateKey);

            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (GeneralSecurityException | IllegalStateException e) {
            throw new DecryptException("RSA decrypt failed", e);
        } catch (RuntimeException e) {
            throw new DecryptException("Unexpected error during decryption", e);
        } finally {
            releaseCipher(cipher, decryptCipherPool);
        }
    }

    /**
     * 加密给定的纯文本字符串，并返回加密后的字符串.
     *
     * @param plainText 要加密的纯文本字符串，使用UTF-8编码.
     * @return 加密后的字符串，Base64编码表示.
     * @throws EncryptException 当加密失败或发生IO操作错误时抛出异常.
     */
    public String encrypt(String plainText) throws EncryptException {
        if (plainText == null) {
            throw new EncryptException("Plain text cannot be null");
        }
        
        Cipher cipher = null;
        try {
            byte[] plainBytes = plainText.getBytes(StandardCharsets.UTF_8);

            cipher = borrowCipherFromPool(encryptCipherPool, "encryption");

            cipher.init(Cipher.ENCRYPT_MODE, publicKey);

            byte[] encryptedBytes = cipher.doFinal(plainBytes);
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (GeneralSecurityException | IllegalStateException e) {
            throw new EncryptException("RSA encrypt failed", e);
        } catch (RuntimeException e) {
            throw new EncryptException("Unexpected error during encryption", e);
        } finally {
            releaseCipher(cipher, encryptCipherPool);
        }
    }

    public String getRsaPublicKey() {
        return base64PublicKey;
    }

    private void releaseCipher(Cipher cipher, ObjectPool<Cipher> cipherPool) {
        if (cipher == null) {
            return;
        }
        try {
            cipherPool.returnObject(cipher);
        } catch (Exception e) {
            log.error("Failed to return Cipher to pool", e);
        }
    }

    /**
     * 从对象池中借用Cipher对象.
     *
     * @param cipherPool Cipher对象池
     * @param operationType 操作类型，用于错误消息
     * @return 借用的Cipher对象
     * @throws DecryptException 当借用过程中发生错误时抛出
     */
    private Cipher borrowCipherFromPool(ObjectPool<Cipher> cipherPool, String operationType) 
            throws DecryptException, EncryptException {
        try {
            return cipherPool.borrowObject();
        } catch (Exception e) {
            if (operationType.equals("decryption")) {
                throw new DecryptException("Failed to borrow cipher from pool for " + operationType, e);
            } else {
                throw new EncryptException("Failed to borrow cipher from pool for " + operationType, e);
            }
        }
    }

    /**
     * Factory method for Cipher pool. Protected for test mocking.
     */
    protected GenericObjectPool<Cipher> createCipherPool(RsaProperties rsaProperties, Key key, int cipher) {
        var encryptFactory = new CipherPooledObjectFactory(key, "RSA/ECB/PKCS1Padding", cipher);
        GenericObjectPool<Cipher> encryptPool = new GenericObjectPool<>(encryptFactory);
        // 最大实例数
        encryptPool.setMaxTotal(rsaProperties.getMaxTotal());
        // 最大空闲
        encryptPool.setMaxIdle(rsaProperties.getMaxIdle());
        // 最小空闲
        encryptPool.setMinIdle(rsaProperties.getMinIdle());
        return encryptPool;
    }

    /**
     * 获取解密Cipher对象池，用于测试.
     *
     * @return 解密Cipher对象池
     */
    protected ObjectPool<Cipher> getDecryptCipherPool() {
        return decryptCipherPool;
    }

    /**
     * 获取加密Cipher对象池，用于测试.
     *
     * @return 加密Cipher对象池
     */
    protected ObjectPool<Cipher> getEncryptCipherPool() {
        return encryptCipherPool;
    }
}
