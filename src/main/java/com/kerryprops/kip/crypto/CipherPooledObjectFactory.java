package com.kerryprops.kip.crypto;

import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.PooledObjectFactory;
import org.apache.commons.pool2.impl.DefaultPooledObject;

import javax.crypto.Cipher;
import java.security.Key;

/**
 * 用于创建和管理 Cipher 对象的工厂.
 * 实现了Apache Commons Pool2的PooledObjectFactory接口，
 * 提供Cipher对象的池化管理功能，支持加密和解密两种模式.
 */
public class CipherPooledObjectFactory implements PooledObjectFactory<Cipher> {

    private final Key key;

    private final String transformation;

    private final int cipherMode;

    /**
     * 构造函数，初始化Cipher工厂.
     *
     * @param key            密钥（可以是公钥或私钥）
     * @param transformation 加密算法/模式/填充
     * @param cipherMode     密码模式（Cipher.ENCRYPT_MODE或Cipher.DECRYPT_MODE）
     */
    public CipherPooledObjectFactory(Key key, String transformation, int cipherMode) {
        this.key = key;
        this.transformation = transformation;
        this.cipherMode = cipherMode;
    }

    @Override
    public PooledObject<Cipher> makeObject() throws Exception {
        Cipher cipher = Cipher.getInstance(transformation);
        cipher.init(cipherMode, key);
        return new DefaultPooledObject<>(cipher);
    }

    @Override
    public void destroyObject(PooledObject<Cipher> p) throws Exception {
        // Cipher对象不需要特殊销毁操作
    }

    @Override
    public boolean validateObject(PooledObject<Cipher> p) {
        return true;
    }

    @Override
    public void activateObject(PooledObject<Cipher> p) throws Exception {
        // 从池中取出时重新初始化
        p.getObject().init(cipherMode, key);
    }

    @Override
    public void passivateObject(PooledObject<Cipher> p) throws Exception {
        // 返回池中时不需要特殊钝化操作
    }

}
