package com.kerryprops.kip.crypto;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * RsaProperties.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> 2025-04-08 09:44:51
 **/
@Data
@Component
@ConfigurationProperties(prefix = "rsa")
public class RsaProperties {

    private String publicKeyPath = "rsa/public_key.pem";

    private String privateKeyPath = "rsa/private_key.pem";

    private int keySize = 2048;

    private int maxTotal = 50;

    private int maxIdle = 10;

    private int minIdle = 2;

}