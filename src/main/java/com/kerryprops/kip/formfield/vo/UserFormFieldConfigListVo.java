package com.kerryprops.kip.formfield.vo;

import com.kerryprops.kip.formfield.UserFormFieldConfig;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * UserFormFieldConfigListVo.
 *
 * <AUTHOR> Yu 2025-03-18 15:07:04
 **/
@Data
public class UserFormFieldConfigListVo {

    @Schema(title = "配置项id")
    private Long id;

    @Schema(title = "表单字段编码")
    private String formFieldCode;

    @Schema(title = "表单字段标题")
    private String formFieldTitle;

    @Schema(title = "是否显示", defaultValue = "ture")
    private Boolean isShow;

    @Schema(title = "是否固定列", defaultValue = "false")
    private Boolean isFixField;

    @Schema(title = "表头顺序", defaultValue = "1")
    private Integer order;

    public static UserFormFieldConfigListVo of(UserFormFieldConfig userFormFieldConfig) {
        UserFormFieldConfigListVo vo = new UserFormFieldConfigListVo();
        vo.setId(userFormFieldConfig.getId());
        vo.setFormFieldCode(userFormFieldConfig.getFormFieldCode());
        vo.setFormFieldTitle(userFormFieldConfig.getFormFieldTitle());
        vo.setIsShow(userFormFieldConfig.getIsShow());
        vo.setIsFixField(userFormFieldConfig.getIsFixField());
        vo.setOrder(userFormFieldConfig.getOrder());
        return vo;
    }

}
