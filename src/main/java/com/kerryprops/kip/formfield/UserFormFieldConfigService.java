package com.kerryprops.kip.formfield;

import com.google.common.collect.Lists;
import com.kerryprops.kip.formfield.dto.UserFormFieldConfigListDto;
import com.kerryprops.kip.formfield.dto.UserFormFieldConfigResetDto;
import com.kerryprops.kip.formfield.vo.UserFormFieldConfigListVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * UserFormFieldConfigService.
 *
 * <AUTHOR> Yu 2025-03-18 15:00:23
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class UserFormFieldConfigService {

    private final UserFormFieldConfigRepository userFormFieldConfigRepository;

    /**
     * 列出用户表单字段配置列表的方法.
     *
     * @param dto 包含查询条件的DTO对象，包括用户ID、服务名称、表单编码等信息.
     * @return 返回一个包含用户表单字段配置信息的列表，其中每个元素为UserFormFieldConfigListVo.
     */
    public List<UserFormFieldConfigListVo> listUserFormFieldConfigs(UserFormFieldConfigListDto dto) {
        var userFormFieldConfigQuery = QUserFormFieldConfig.userFormFieldConfig;
        var searchCriteria = userFormFieldConfigQuery.userId.eq(dto.getUserId())
                .and(userFormFieldConfigQuery.serviceName.eq(dto.getServiceName()))
                .and(userFormFieldConfigQuery.formCode.eq(dto.getFormCode()));
        var vos = userFormFieldConfigRepository.findAll(searchCriteria);
        return Lists.newArrayList(vos).stream().map(UserFormFieldConfigListVo::of).toList();
    }

    /**
     * 重置用户表单字段配置的方法.
     *
     * @param dto 包含重置信息的DTO对象，包括用户ID、服务名称、表单编码以及新的表单字段配置信息.
     * @return 返回一个包含更新后用户表单字段配置信息的列表，其中每个元素为UserFormFieldConfigListVo.
     */
    public List<UserFormFieldConfigListVo> resetUserFormFieldConfigs(UserFormFieldConfigResetDto dto) {
        long deletedCount = userFormFieldConfigRepository.deleteUserFormFieldConfig(dto.getUserId(),
                dto.getServiceName(), dto.getFormCode());
        log.info("deletedCount : {}", deletedCount);
        var userFormFieldConfigs = dto.toUserFormFieldConfigs();
        userFormFieldConfigRepository.saveAll(userFormFieldConfigs);
        return userFormFieldConfigs.stream().map(UserFormFieldConfigListVo::of).toList();
    }

}
