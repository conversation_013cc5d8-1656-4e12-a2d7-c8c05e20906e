package com.kerryprops.kip.formfield;

import com.kerryprops.kip.formfield.dto.UserFormFieldConfigListDto;
import com.kerryprops.kip.formfield.dto.UserFormFieldConfigResetDto;
import com.kerryprops.kip.formfield.vo.UserFormFieldConfigListVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * UserFormFiledConfigController.
 *
 * <AUTHOR> Yu 2025-03-17 16:36:14
 **/
@Tag(name = "S端-用户表单字段配置")
@RestController
@RequestMapping("/s/user/form_field_configs")
@RequiredArgsConstructor
public class UserFormFiledConfigController {

    private final UserFormFieldConfigService userFormFieldConfigService;

    @Operation(summary = "查询表单字段配置列表")
    @GetMapping
    public List<UserFormFieldConfigListVo> listUserFormFieldConfigs(@Valid UserFormFieldConfigListDto dto) {
        return userFormFieldConfigService.listUserFormFieldConfigs(dto);
    }

    @Operation(summary = "重置表单字段配置")
    @PostMapping("/reset")
    public List<UserFormFieldConfigListVo> resetUserFormFieldConfigs(@Valid @RequestBody UserFormFieldConfigResetDto dto) {
        return userFormFieldConfigService.resetUserFormFieldConfigs(dto);
    }

}
