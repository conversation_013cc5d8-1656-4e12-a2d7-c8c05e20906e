package com.kerryprops.kip.formfield.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * FormFields.
 *
 * <AUTHOR> 2025-03-18 15:38:52
 **/
@Data
public class FormFields {

    @NotBlank
    @Schema(title = "表单字段编码")
    private String formFieldCode;

    @Schema(title = "表单字段标题")
    private String formFieldTitle;

    @Schema(title = "是否显示", defaultValue = "ture")
    private Boolean isShow;

    @Schema(title = "是否固定列", defaultValue = "false")
    private Boolean isFixField;

    @NotNull
    @Schema(title = "表头顺序")
    private Integer order;

}
