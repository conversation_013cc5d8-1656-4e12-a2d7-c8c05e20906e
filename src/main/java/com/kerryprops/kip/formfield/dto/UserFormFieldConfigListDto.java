package com.kerryprops.kip.formfield.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * UserFormFieldConfigListDto.
 *
 * <AUTHOR> 2025-03-18 15:05:59
 **/
@Data
public class UserFormFieldConfigListDto {

    @NotBlank
    @Schema(title = "用户id")
    private String userId;

    @NotBlank
    @Schema(title = "服务名称")
    private String serviceName;

    @NotBlank
    @Schema(title = "表单编码")
    private String formCode;

}
