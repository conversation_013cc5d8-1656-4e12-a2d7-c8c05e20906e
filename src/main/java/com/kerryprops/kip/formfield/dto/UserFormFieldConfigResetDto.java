package com.kerryprops.kip.formfield.dto;

import com.kerryprops.kip.formfield.UserFormFieldConfig;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * UserFormFieldConfigResetDto.
 *
 * <AUTHOR> Yu 2025-03-18 15:36:17
 **/
@Data
public class UserFormFieldConfigResetDto {

    @NotBlank
    @Schema(title = "用户id")
    private String userId;

    @NotBlank
    @Schema(title = "服务名称")
    private String serviceName;

    @NotBlank
    @Schema(title = "表单编码")
    private String formCode;

    @NotEmpty
    @Schema(title = "表单字段")
    private List<@Valid FormFields> formFields;

    public List<UserFormFieldConfig> toUserFormFieldConfigs() {
        return formFields.stream().map(formField -> {
            UserFormFieldConfig config = new UserFormFieldConfig();
            config.setUserId(userId);
            config.setServiceName(serviceName);
            config.setFormCode(formCode);
            config.setFormFieldCode(formField.getFormFieldCode());
            config.setFormFieldTitle(formField.getFormFieldTitle());
            config.setIsShow(formField.getIsShow());
            config.setIsFixField(formField.getIsFixField());
            config.setOrder(formField.getOrder());
            return config;
        }).toList();
    }

}
