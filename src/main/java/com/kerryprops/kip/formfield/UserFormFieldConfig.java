package com.kerryprops.kip.formfield;

import com.kerryprops.kip.common.jpa.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * 用户表单字段配置.
 *
 * <AUTHOR> 2024-06-21 16:58:40
 **/
@Getter
@Setter
@Entity
@DynamicInsert
@DynamicUpdate
@Table(name = "tb_user_form_field_config")
@ToString(callSuper = true)
public class UserFormFieldConfig extends BaseEntity {

    @Schema(title = "用户id")
    @Column(name = "user_id", length = 36)
    private String userId;

    @Schema(title = "服务名称")
    @Column(name = "service_name", length = 50)
    private String serviceName;

    @Schema(title = "表单编码")
    @Column(name = "form_code", length = 50)
    private String formCode;

    @Schema(title = "表单字段编码")
    @Column(name = "form_field_code", length = 50)
    private String formFieldCode;

    @Schema(title = "表单字段标题")
    @Column(name = "form_field_title", length = 100)
    private String formFieldTitle;

    @Schema(title = "是否显示", defaultValue = "ture")
    @Column(name = "is_show")
    private Boolean isShow;

    @Schema(title = "是否固定列", defaultValue = "false")
    @Column(name = "is_fix_field")
    private Boolean isFixField;

    @Schema(title = "表头顺序", defaultValue = "1")
    @Column(name = "field_order")
    private Integer order;

}
