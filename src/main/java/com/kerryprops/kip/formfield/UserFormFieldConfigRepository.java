package com.kerryprops.kip.formfield;

import com.kerryprops.kip.common.jpa.BaseRepository;
import jakarta.transaction.Transactional;

/**
 * UserFormFieldConfigRepo.
 *
 * <AUTHOR> Yu 2025-03-18 14:59:14
 **/
public interface UserFormFieldConfigRepository extends BaseRepository<UserFormFieldConfig> {

    /**
     * 删除用户表单字段配置信息.
     *
     * @param userId      用户ID
     * @param serviceName 服务名称
     * @param formCode    表单编码
     * @return 被删除的记录数
     */
    @Transactional
    default long deleteUserFormFieldConfig(String userId, String serviceName, String formCode) {
        var userFormFieldConfigQuery = QUserFormFieldConfig.userFormFieldConfig;
        var searchCriteria = userFormFieldConfigQuery.userId.eq(userId)
                .and(userFormFieldConfigQuery.serviceName.eq(serviceName))
                .and(userFormFieldConfigQuery.formCode.eq(formCode));
        return getQuery().delete(userFormFieldConfigQuery)
                .where(searchCriteria)
                .execute();
    }

}
