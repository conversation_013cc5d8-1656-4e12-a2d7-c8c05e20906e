package com.kerryprops.kip;

import com.kerryprops.kip.common.jpa.JpaAdaptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * Spring Boot应用启动器类.
 * 此类是Spring Boot应用程序的入口点，负责启动和初始化整个应用.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> <PERSON>
 * Created Date - 2025/2/28 10:14
 */
@Slf4j
@SpringBootApplication
@EnableJpaRepositories(repositoryBaseClass = JpaAdaptor.class)
public class ApplicationLauncher {

    public static void main(String[] args) {
        SpringApplication.run(ApplicationLauncher.class, args);
    }

}
