package com.kerryprops.kip.exception;

import com.kerryprops.kip.exception.support.AbstractAppException;
import com.kerryprops.kip.exception.support.ExceptionCode;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * 未认证异常.
 *
 * <p>
 * 当客户端未能通过身份验证时抛出此异常，对应HTTP状态码401 Unauthorized.
 * </p>
 *
 * <AUTHOR>
 */
@ResponseStatus(HttpStatus.UNAUTHORIZED)
public class UnauthorizedException extends AbstractAppException {

    private static final long serialVersionUID = 1L;

    /**
     * 使用默认消息构造异常.
     */
    public UnauthorizedException() {
        super(ExceptionCode.UNAUTHORIZED, null, "未经授权的访问", null);
    }

    /**
     * 使用自定义消息构造异常.
     *
     * @param message 异常消息
     */
    public UnauthorizedException(String message) {
        super(ExceptionCode.UNAUTHORIZED, null, message, null);
    }

    /**
     * 使用自定义消息和原因构造异常.
     *
     * @param message 异常消息
     * @param cause   异常原因
     */
    public UnauthorizedException(String message, Throwable cause) {
        super(ExceptionCode.UNAUTHORIZED, null, message, cause);
    }

}
