package com.kerryprops.kip.exception;

import com.kerryprops.kip.exception.support.AbstractAppException;
import com.kerryprops.kip.exception.support.ExceptionCode;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * 请求参数无效异常.
 *
 * <p>
 * 当客户端发送了无效的请求，例如缺少必要的请求参数时抛出此异常，对应HTTP状态码400 Bad Request.
 * </p>
 *
 * <AUTHOR>
 */
@ResponseStatus(HttpStatus.BAD_REQUEST)
public class BadRequestException extends AbstractAppException {

    private static final long serialVersionUID = 1L;

    /**
     * 使用默认消息构造异常.
     */
    public BadRequestException() {
        super(ExceptionCode.BAD_REQUEST, null, "请求参数无效", null);
    }

    /**
     * 使用自定义消息构造异常.
     *
     * @param message 异常消息
     */
    public BadRequestException(String message) {
        super(ExceptionCode.BAD_REQUEST, null, message, null);
    }

    /**
     * 使用自定义消息和原因构造异常.
     *
     * @param message 异常消息
     * @param cause   异常原因
     */
    public BadRequestException(String message, Throwable cause) {
        super(ExceptionCode.BAD_REQUEST, null, message, cause);
    }

}
