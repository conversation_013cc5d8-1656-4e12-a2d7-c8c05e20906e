package com.kerryprops.kip.exception.support;

import com.kerryprops.kip.common.AppConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import org.slf4j.MDC;
import org.springframework.lang.Nullable;

import java.time.ZonedDateTime;
import java.util.Objects;

/**
 * 该类用于封装异常信息.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * @param errorCode    - 错误码
 * @param errorMessage - 错误信息
 * @param detailInfo   - 详细信息
 * <AUTHOR> <PERSON>
 * Created Date - 2022/11/9 7:05
 * @see ExceptionHandlers
 */
@Schema(description = "异常信息")
public record ExceptionRepresentation(
        @Schema(description = "错误码",
                example = "NOT_FOUND") LocalizableMessage errorCode,
        @Schema(description = "错误信息",
                example = "资源未找到") String errorMessage,
        @Schema(description = "详细信息",
                example = "Resource not found") @Nullable Object detailInfo) {

    public ExceptionRepresentation {
        Objects.requireNonNull(errorCode);
        Objects.requireNonNull(errorMessage);
    }

    @Schema(description = "日志跟踪ID",
            example = "KEP-8757-5f8cbb03cc5d")
    public String getCorrelationId() {
        return MDC.get(AppConstants.MDC_CORRELATION_ID_KEY);
    }

    @Schema(description = "时间戳",
            example = "2022-11-09 07:05:00")
    public ZonedDateTime getTimeStamp() {
        return ZonedDateTime.now();
    }

}
