package com.kerryprops.kip.exception.support;

import com.kerryprops.kip.common.lock.Lock;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.Objects;

/**
 * 推荐的异常抛出方式，一个业务对应一个异常类，业务内部具体的异常通过定义ExceptionCode的方式表示，
 * 比如发票业务中由于invoice service接口失败，可以在ExceptionCode中定义具体的异常信息(code,message)
 * 然后 throw new InvoiceException(NEW_DEFINED_CODE, message)，类似如下这样:
 * <pre>
 * {@code
 * public Object simpleErr() {
 *   throw new NOT_FOUND(NOT_FOUND, "detailed message");
 * }
 * }
 * </pre>
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Sam Zhang
 * Created Date - 2022/11/9 7:05
 */
@Getter
@RequiredArgsConstructor
public enum ExceptionCode implements LocalizableMessage {

    /**
     * 当用户重复提交表单造成数据冲突时，会抛出此异常.前提是加上注解.
     * {@link Lock} .
     */
    LOCK_OCCUPIED("请勿重复提交.", "Please do not resubmit."),
    /**
     * 系统繁忙，请稍后重试 (System is busy, please try again later).
     */
    UNKNOWN_ERROR("未知错误", "Unknown error"),

    /**
     * 参数异常，请重试 (Params error).
     */
    VALIDATION_FAILED("参数异常，请重试", "Params error"),

    /**
     * 资源未找到 (Resource not found).
     */
    NOT_FOUND("资源未找到", "Resource not found"),

    /**
     * 资源未找到 (Resource not found) - HTTP 404.
     */
    RESOURCE_NOT_FOUND("请求的资源不存在", "Requested resource not found"),

    /**
     * 请求参数无效 (Bad request) - HTTP 400.
     */
    BAD_REQUEST("请求参数无效", "Invalid request parameters"),

    /**
     * 未认证 (Unauthorized) - HTTP 401.
     */
    UNAUTHORIZED("未经授权的访问", "Unauthorized access"),

    /**
     * 禁止访问 (Forbidden) - HTTP 403.
     */
    FORBIDDEN("禁止访问该资源", "Access to this resource is forbidden"),

    /**
     * 服务器内部错误 (Internal server error) - HTTP 500.
     */
    INTERNAL_SERVER_ERROR("服务器内部错误", "Internal server error"),

    /**
     * 服务不可用 (Service unavailable) - HTTP 503.
     */
    SERVICE_UNAVAILABLE("服务暂时不可用", "Service temporarily unavailable"),

    /**
     * 操作过于频繁，请稍后重试 (Operation is too frequent, please try again later).
     */
    OPERATION_FREQUENTLY("操作过于频繁，请稍后重试", "Operation is too frequent, please try again later"),

    /**
     * HTTP方法不被允许 (Method Not Allowed) - HTTP 405.
     */
    METHOD_NOT_ALLOWED("请求方法不被允许", "Method not allowed"),
    /**
     * 请求其他服务异常.
     */
    REMOTE_SERVICE_ERROR("请求其他服务失败", "access other server fail");

    private final String messageCn;

    private final String messageEn;

    @Nullable
    private Object[] args;

    @Override
    public String toString() {
        return String.format("ExceptionCode{messageCn='%s', messageEn='%s'}", messageCn, messageEn);
    }

    @Override
    public Object[] getArgs() {
        if (Objects.nonNull(args)) {
            return Arrays.copyOf(args, args.length);
        }
        return new Object[0];
    }

    public LocalizableMessage withArgs(Object... args) {
        this.args = Arrays.copyOf(args, args.length);
        return this;
    }

}
