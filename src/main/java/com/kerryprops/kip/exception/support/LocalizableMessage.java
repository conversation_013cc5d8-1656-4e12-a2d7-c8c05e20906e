package com.kerryprops.kip.exception.support;

import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Locale;

/**
 * 该接口用于定义异常信息的本地化处理.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> <PERSON>
 * Created Date - 2022/11/9 7:05
 */
public sealed interface LocalizableMessage permits ExceptionCode {

    /**
     * 检查当前语言是否为简体中文.
     *
     * @return 如果是简体中文返回true，否则返回false
     */
    static boolean isSimplifiedChinese() {
        var locale = LocaleContextHolder.getLocale();
        return Locale.SIMPLIFIED_CHINESE.getLanguage().equals(locale.getLanguage());
    }

    /**
     * 获取中文消息描述.
     *
     * @return 消息
     */
    String getMessageCn();

    /**
     * 获取英文消息描述.
     *
     * @return 消息
     */
    String getMessageEn();

    /**
     * 获取格式化参数.
     *
     * @return 参数
     */
    default Object[] getArgs() {
        return new Object[0];
    }

    /**
     * 获取最终语言切换后的信息.
     *
     * @return 最终描述
     */
    default String getLangMessage() {
        var message = isSimplifiedChinese() ? getMessageCn() : getMessageEn();
        return formatMessage(message, getArgs());
    }

    /**
     * 格式化消息字符串.
     *
     * @param message 消息模板
     * @param args    格式化参数
     * @return 格式化后的消息字符串
     */
    private String formatMessage(String message, Object... args) {
        return String.format(message, args);
    }

}
