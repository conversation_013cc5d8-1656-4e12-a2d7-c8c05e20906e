package com.kerryprops.kip.exception.support;

import com.kerryprops.kip.exception.ApiAccessException;
import com.kerryprops.kip.exception.BadRequestException;
import com.kerryprops.kip.exception.ForbiddenAccessException;
import com.kerryprops.kip.exception.ResourceNotFoundException;
import com.kerryprops.kip.exception.UnauthorizedException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 该类用于处理异常.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <p>
 * 本类实现了全局异常处理，根据不同异常类型返回对应的HTTP状态码：
 * - 400 Bad Request：客户端发送了无效的请求，例如缺少必要的请求参数
 * - 401 Unauthorized：客户端未能通过身份验证
 * - 403 Forbidden：客户端通过了身份验证，但没有权限访问请求的资源
 * - 404 Not Found：请求的资源不存在
 * - 500 Internal Server Error：服务器在处理请求时发生了错误
 * </p>
 *
 * <AUTHOR> Sam Zhang
 * Created Date - 2022/11/9 7:05
 */
@Slf4j
@ResponseBody
@ControllerAdvice
public class ExceptionHandlers {

    /**
     * 处理参数校验异常，返回400 Bad Request.
     *
     * <p>
     * 400 Bad Request：当使用@Validated进行参数校验失败时抛出，例如必填参数为空或格式不正确.
     * </p>
     *
     * @param ex 抛出的ConstraintViolationException异常
     * @return 包含校验错误信息的异常表示
     * @since 1.0.0
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ExceptionRepresentation handleConstraintViolation(ConstraintViolationException ex) {
        Set<ConstraintViolation<?>> violations = ex.getConstraintViolations();
        String errorMessage = violations.stream()
                .map(violation -> {
                    String path = violation.getPropertyPath().toString();
                    // Extract the method parameter name if available
                    if (path.contains(".")) {
                        path = path.substring(path.lastIndexOf('.') + 1);
                    }
                    return String.format("%s: %s", path, violation.getMessage());
                })
                .collect(Collectors.joining("; "));
        
        log.warn("Validation failed: {}", errorMessage);
        return ExceptionRepresentationFactory.mappingRepresentation(
                ExceptionCode.BAD_REQUEST,
                errorMessage
        );
    }

    /**
     * 处理未捕获的异常，返回500 Internal Server Error.
     *
     * <p>
     * 500 Internal Server Error：服务器在处理请求时发生了错误.
     * </p>
     *
     * @param ex 抛出的异常
     * @return 异常信息
     */
    @ExceptionHandler(Throwable.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ExceptionRepresentation handleThrowable(Throwable ex) {
        log.error("Unhandled exception: {}", ex.getMessage(), ex);
        return ExceptionRepresentationFactory.mappingRepresentation(ExceptionCode.INTERNAL_SERVER_ERROR,
                                                                    ex.getMessage());
    }

    /**
     * 处理400 Bad Request异常.
     *
     * <p>
     * 400 Bad Request：客户端发送了无效的请求，例如缺少必要的请求参数.
     * </p>
     *
     * @param ex 抛出的异常
     * @return 异常信息
     */
    @ExceptionHandler(BadRequestException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ExceptionRepresentation handleBadRequestException(BadRequestException ex) {
        log.warn("Bad request: {}", ex.getMessage(), ex);
        return ExceptionRepresentationFactory.mappingRepresentation(ex);
    }

    /**
     * 处理401 Unauthorized异常.
     *
     * <p>
     * 401 Unauthorized：客户端未能通过身份验证.
     * </p>
     *
     * @param ex 抛出的异常
     * @return 异常信息
     */
    @ExceptionHandler(UnauthorizedException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public ExceptionRepresentation handleUnauthorizedException(UnauthorizedException ex) {
        log.warn("Unauthorized: {}", ex.getMessage(), ex);
        return ExceptionRepresentationFactory.mappingRepresentation(ex);
    }

    /**
     * 处理403 Forbidden异常.
     *
     * <p>
     * 403 Forbidden：客户端通过了身份验证，但没有权限访问请求的资源.
     * </p>
     *
     * @param ex 抛出的异常
     * @return 异常信息
     */
    @ExceptionHandler(ForbiddenAccessException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public ExceptionRepresentation handleForbiddenAccessException(ForbiddenAccessException ex) {
        log.warn("Forbidden: {}", ex.getMessage(), ex);
        return ExceptionRepresentationFactory.mappingRepresentation(ex);
    }

    /**
     * 处理404 Not Found异常.
     *
     * <p>
     * 404 Not Found：请求的资源不存在.
     * </p>
     *
     * @param ex 抛出的异常
     * @return 异常信息
     */
    @ExceptionHandler(ResourceNotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ExceptionRepresentation handleResourceNotFoundException(ResourceNotFoundException ex) {
        log.warn("Resource not found: {}", ex.getMessage(), ex);
        return ExceptionRepresentationFactory.mappingRepresentation(ex);
    }

    /**
     * 处理其他应用异常，默认返回400 Bad Request.
     *
     * <p>
     * 对于未明确指定HTTP状态码的应用异常，默认返回400 Bad Request.
     * </p>
     *
     * @param ex 抛出的异常
     * @return 异常信息
     */
    @ExceptionHandler(AbstractAppException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ExceptionRepresentation handleAbstractAppException(AbstractAppException ex) {
        log.error("Application exception: {}", ex.getMessage(), ex);
        return ExceptionRepresentationFactory.mappingRepresentation(ex);
    }

    /**
     * Handle HTTP request method not supported exception and return 405 Method Not Allowed.
     *
     * <p>
     * 405 Method Not Allowed:
     * The method specified in the request is not allowed for the resource identified by the request URI.
     * </p>
     *
     * @param ex the exception thrown
     * @return exception information
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(org.springframework.http.HttpStatus.METHOD_NOT_ALLOWED)
    public ExceptionRepresentation handleMethodNotAllowed(HttpRequestMethodNotSupportedException ex) {
        log.warn("Method not allowed: {}", ex.getMessage(), ex);
        return ExceptionRepresentationFactory.mappingRepresentation(ExceptionCode.METHOD_NOT_ALLOWED, ex.getMessage());
    }

    /**
     * Handle HttpMessageNotReadableException and return 400 Bad Request.
     *
     * <p>
     * 400 Bad Request: The request could not be understood by the server due to malformed syntax or missing body.
     * </p>
     *
     * @param ex the exception thrown
     * @return exception information
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseStatus(org.springframework.http.HttpStatus.BAD_REQUEST)
    public ExceptionRepresentation handleHttpMessageNotReadable(HttpMessageNotReadableException ex) {
        log.warn("Request body missing or unreadable: {}", ex.getMessage(), ex);
        return ExceptionRepresentationFactory.mappingRepresentation(ExceptionCode.BAD_REQUEST, ex.getMessage());
    }

    @ExceptionHandler(ApiAccessException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ExceptionRepresentation handleApiAccessException(ApiAccessException ex) {
        log.warn("Api access fail: {}", ex.getMessage(), ex);
        return ExceptionRepresentationFactory.mappingRepresentation(ex);
    }

}
