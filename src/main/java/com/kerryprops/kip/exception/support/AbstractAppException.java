package com.kerryprops.kip.exception.support;

import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.Nullable;

/**
 * 该类用于定义应用异常的基本结构.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> <PERSON>
 * Created Date - 2022/11/9 7:05
 */
@Getter
@Setter
public abstract class AbstractAppException extends RuntimeException {

    private final transient LocalizableMessage code;

    @Nullable
    private final transient Object detailInfo;

    protected AbstractAppException(LocalizableMessage code, @Nullable Object detailInfo, @Nullable String message,
                                   @Nullable Throwable cause) {
        super(message, cause);
        this.code = code;
        this.detailInfo = detailInfo;
    }

}
