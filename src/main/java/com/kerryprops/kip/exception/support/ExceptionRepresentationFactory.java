package com.kerryprops.kip.exception.support;

/**
 * ExceptionRepresentationFactory
 * 该类用于封装异常信息.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> <PERSON>
 * Created Date - 2024/6/14 下午2:38
 */
final class ExceptionRepresentationFactory {

    private ExceptionRepresentationFactory() {
    }

    public static ExceptionRepresentation mappingRepresentation(LocalizableMessage code) {
        return mappingRepresentation(code, code.getMessageEn());
    }

    public static ExceptionRepresentation mappingRepresentation(LocalizableMessage code, Object detailInfo) {
        return new ExceptionRepresentation(code, code.getLangMessage(), detailInfo);
    }

    public static ExceptionRepresentation mappingRepresentation(AbstractAppException ex) {
        return new ExceptionRepresentation(ex.getCode(), ex.getCode().getLangMessage(), ex.getDetailInfo());
    }

    public static ExceptionRepresentation mappingNotFoundRep(RuntimeException ex) {
        return new ExceptionRepresentation(ExceptionCode.NOT_FOUND, ExceptionCode.NOT_FOUND.getLangMessage(), ex
                .getMessage());
    }

}
