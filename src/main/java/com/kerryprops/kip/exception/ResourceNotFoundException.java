package com.kerryprops.kip.exception;

import com.kerryprops.kip.exception.support.AbstractAppException;
import com.kerryprops.kip.exception.support.ExceptionCode;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * 资源未找到异常.
 *
 * <p>
 * 当请求的资源不存在时抛出此异常，对应HTTP状态码404 Not Found.
 * </p>
 *
 * <AUTHOR>
 */
@ResponseStatus(HttpStatus.NOT_FOUND)
public class ResourceNotFoundException extends AbstractAppException {

    private static final long serialVersionUID = 1L;

    /**
     * 使用默认消息构造异常.
     */
    public ResourceNotFoundException() {
        super(ExceptionCode.RESOURCE_NOT_FOUND, null, "请求的资源不存在", null);
    }

    /**
     * 使用自定义消息构造异常.
     *
     * @param message 异常消息
     */
    public ResourceNotFoundException(String message) {
        super(ExceptionCode.RESOURCE_NOT_FOUND, null, message, null);
    }

    /**
     * 使用自定义消息和原因构造异常.
     *
     * @param message 异常消息
     * @param cause   异常原因
     */
    public ResourceNotFoundException(String message, Throwable cause) {
        super(ExceptionCode.RESOURCE_NOT_FOUND, null, message, cause);
    }

}
