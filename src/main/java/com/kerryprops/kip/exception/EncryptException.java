package com.kerryprops.kip.exception;

/**
 * 加密过程中的异常类.
 * 用于封装加密过程中可能发生的各种异常情况，提供更清晰的错误信息.
 */
public class EncryptException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 使用指定的错误消息构造一个新的加密异常.
     *
     * @param message 详细错误信息
     */
    public EncryptException(String message) {
        super(message);
    }

    /**
     * 使用指定的错误消息和原因构造一个新的加密异常.
     *
     * @param message 详细错误信息
     * @param cause   导致此异常的原因
     */
    public EncryptException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 使用指定的原因构造一个新的加密异常.
     *
     * @param cause 导致此异常的原因
     */
    public EncryptException(Throwable cause) {
        super(cause);
    }

    /**
     * 使用指定的详细信息、原因、是否启用禁止抑制和是否启用可写堆栈跟踪构造一个新的加密异常.
     *
     * @param message            详细错误信息
     * @param cause              导致此异常的原因
     * @param enableSuppression  是否启用或禁用抑制
     * @param writableStackTrace 是否启用或禁用可写堆栈跟踪
     */
    public EncryptException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

}
