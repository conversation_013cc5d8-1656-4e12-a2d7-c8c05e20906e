package com.kerryprops.kip.exception;


import com.kerryprops.kip.exception.support.AbstractAppException;
import com.kerryprops.kip.exception.support.ExceptionCode;

/**
 * 解密过程中发生的异常.
 * 封装了解密操作中可能出现的各种错误情况，提供多种构造方法以支持不同的异常场景.
 */
public class DecryptException extends AbstractAppException {

    /**
     * 使用默认消息构造异常.
     */
    public DecryptException() {
        super(ExceptionCode.UNKNOWN_ERROR, null, "非预期异常", null);
    }

    /**
     * 使用指定消息和原因构造异常.
     *
     * @param message 异常消息
     * @param cause   原始异常
     */
    public DecryptException(String message, Throwable cause) {
        super(ExceptionCode.UNKNOWN_ERROR, null, message, cause);
    }

    /**
     * 使用指定消息构造异常.
     *
     * @param message 异常消息
     */
    public DecryptException(String message) {
        super(ExceptionCode.UNKNOWN_ERROR, null, message, null);
    }

    /**
     * 使用指定异常码和消息构造异常.
     *
     * @param code    异常码
     * @param message 异常消息
     */
    public DecryptException(ExceptionCode code, String message) {
        super(code, null, message, null);
    }

    /**
     * 使用指定异常码、消息和原因构造异常.
     *
     * @param code    异常码
     * @param message 异常消息
     * @param cause   原始异常
     */
    public DecryptException(ExceptionCode code, String message, Throwable cause) {
        super(code, null, message, cause);
    }

}
