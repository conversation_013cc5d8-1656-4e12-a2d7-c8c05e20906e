package com.kerryprops.kip.exception;

import com.kerryprops.kip.exception.support.AbstractAppException;
import com.kerryprops.kip.exception.support.ExceptionCode;
import com.kerryprops.kip.exception.support.LocalizableMessage;
import org.springframework.lang.Nullable;

/**
 * UnknownException
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> <PERSON>
 * Created Date - 2024/6/28 下午6:08
 */
public class UnknownException extends AbstractAppException {

    public UnknownException(Throwable throwable) {
        this(ExceptionCode.UNKNOWN_ERROR, null, null, throwable);
    }

    public UnknownException() {
        this(ExceptionCode.UNKNOWN_ERROR, null, null, null);
    }

    protected UnknownException(LocalizableMessage code, @Nullable Object detailInfo, @Nullable String message,
                               @Nullable Throwable cause) {
        super(code, detailInfo, message, cause);
    }

}
