package com.kerryprops.kip.exception;

import com.kerryprops.kip.exception.support.AbstractAppException;
import com.kerryprops.kip.exception.support.ExceptionCode;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * 进行第三方接口访问异常.
 */
@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
public class ApiAccessException extends AbstractAppException {

    private static final long serialVersionUID = 1L;

    /**
     * 使用自定义消息构造异常.
     *
     * @param message 异常消息
     */
    public ApiAccessException(String message) {
        super(ExceptionCode.REMOTE_SERVICE_ERROR, message, message, null);
    }

}
