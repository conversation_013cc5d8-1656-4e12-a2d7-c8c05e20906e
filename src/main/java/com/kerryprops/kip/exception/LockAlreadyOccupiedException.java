package com.kerryprops.kip.exception;

import com.kerryprops.kip.exception.support.AbstractAppException;
import com.kerryprops.kip.exception.support.ExceptionCode;
import com.kerryprops.kip.exception.support.LocalizableMessage;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.function.Supplier;

/**
 * 该类用于定义锁已被占用异常.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> <PERSON>
 * Created Date - 2023/02/21 10:05
 */
public class LockAlreadyOccupiedException extends AbstractAppException {

    public LockAlreadyOccupiedException(LocalizableMessage code, @Nullable Object detailInfo, @Nullable String message,
                                        @Nullable Throwable cause) {
        super(code, detailInfo, message, cause);
    }

    public static Supplier<LockAlreadyOccupiedException> lockAlreadyOccupiedException(List<String> keys) {
        var message = String.join(",", keys);
        return () -> new LockAlreadyOccupiedException(ExceptionCode.LOCK_OCCUPIED, message + "已被锁定", message + "已被锁定", null);
    }

}
