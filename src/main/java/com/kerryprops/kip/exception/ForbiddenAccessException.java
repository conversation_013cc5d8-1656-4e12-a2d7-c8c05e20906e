package com.kerryprops.kip.exception;

import com.kerryprops.kip.exception.support.AbstractAppException;
import com.kerryprops.kip.exception.support.ExceptionCode;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * 禁止访问异常.
 *
 * <p>
 * 当客户端通过了身份验证，但没有权限访问请求的资源时抛出此异常，对应HTTP状态码403 Forbidden.
 * </p>
 *
 * <AUTHOR>
 */
@ResponseStatus(HttpStatus.FORBIDDEN)
public class ForbiddenAccessException extends AbstractAppException {

    private static final long serialVersionUID = 1L;

    /**
     * 使用默认消息构造异常.
     */
    public ForbiddenAccessException() {
        super(ExceptionCode.FORBIDDEN, null, "禁止访问该资源", null);
    }

    /**
     * 使用自定义消息构造异常.
     *
     * @param message 异常消息
     */
    public ForbiddenAccessException(String message) {
        super(ExceptionCode.FORBIDDEN, null, message, null);
    }

    /**
     * 使用自定义消息和原因构造异常.
     *
     * @param message 异常消息
     * @param cause   异常原因
     */
    public ForbiddenAccessException(String message, Throwable cause) {
        super(ExceptionCode.FORBIDDEN, null, message, cause);
    }

}
