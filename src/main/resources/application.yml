spring:
  profiles.active: ${DEPLOYED_ENV}
  application.name: toolkit-service
  servlet.multipart.enabled: false
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
  mvc:
    format:
      date-time: yyyy-MM-dd HH:mm:ss
  cloud:
    openfeign:
      client:
        config:
          default:
            loggerLevel: full
            connectTimeout: 5000
            readTimeout: 5000
management:
  endpoints:
    web:
      exposure:
        include: "*"
      base-path: /actuator
  endpoint:
    loggers:
      enabled: true
app:
  id: ${spring.application.name}
logging:
  level:
    org.zalando.logbook.Logbook: trace
logbook:
  write:
    max-body-size: 5000
  predicate:
    exclude:
      - path: /actuator/**
        methods:
          - GET
      - path: /v3/api-docs
        methods:
          - GET
      - path: /health/shallow
        methods:
          - GET