package com.kerryprops.kip.riskcontrol.rules;

import com.kerryprops.kip.riskcontrol.model.RiskAssessmentContext;
import com.kerryprops.kip.riskcontrol.model.IpGeolocation;
import com.kerryprops.kip.riskcontrol.constant.RiskResult
import com.kerryprops.kip.riskcontrol.general.RiskResult;

/**
 * Risk assessment rules specific to PARKING_BENEFIT business scenario.
 * 
 * Business Rules:
 * - 非大陆IP -> N (拦截) - "缴费地为非中国大陆，暂无法使用会员停车礼遇"
 * - 负积分 -> N (拦截)
 *
 * <AUTHOR> Properties Limited
 * @since 1.0
 */

// ========== IP归属地规则 ==========

// 会员停车权益：非大陆IP -> N (拦截)
rule "Parking Benefit - Non-mainland IP - REJECT"
    salience 100
    when
        $context : RiskAssessmentContext(
            businessScenario == RiskAssessmentContext.BusinessScenario.PARKING_BENEFIT,
            ipGeolocation != null,
            mallCode == RiskAssessmentContext.MallCode.JAKC || mallCode == RiskAssessmentContext.MallCode.HKC || mallCode == RiskAssessmentContext.MallCode.KP,
            riskResult == null
        )
        $geolocation : IpGeolocation(fromMainlandChina == false) from $context.ipGeolocation
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setBlockMessage("缴费地为非中国大陆，暂无法使用会员停车礼遇");
        $context.setAssessmentDetails("会员停车权益：非大陆IP被拦截");
        System.out.println("Rule executed: Parking Benefit - Non-mainland IP - REJECT");
end

// ========== 积分相关规则 ==========

// 会员停车权益：负积分 -> N (拦截)
rule "Parking Benefit - Negative Points - REJECT"
    salience 80
    when
        $context : RiskAssessmentContext(
            businessScenario == RiskAssessmentContext.BusinessScenario.PARKING_BENEFIT,
            memberPoints != null,
            memberPoints < 0,
            mallCode == RiskAssessmentContext.MallCode.JAKC || mallCode == RiskAssessmentContext.MallCode.HKC || mallCode == RiskAssessmentContext.MallCode.KP,
            riskResult == null
        )
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setBlockMessage("TBD");
        $context.setAssessmentDetails("会员停车权益：负积分被拦截");
        System.out.println("Rule executed: Parking Benefit - Negative Points - REJECT");
end
