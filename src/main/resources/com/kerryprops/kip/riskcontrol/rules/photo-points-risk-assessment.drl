package com.kerryprops.kip.riskcontrol.rules;

import com.kerryprops.kip.riskcontrol.model.RiskAssessmentContext;
import com.kerryprops.kip.riskcontrol.model.IpGeolocation;
import com.kerryprops.kip.riskcontrol.constant.RiskResult
import com.kerryprops.kip.riskcontrol.general.RiskResult;

/**
 * Risk assessment rules specific to PHOTO_POINTS business scenario.
 * 
 * Business Rules:
 * - 非大陆IP -> N (拦截) - "该功能无法使用，请至礼宾台"
 * - 非大陆手机号 -> N (拦截)
 * - 虚拟号段 -> N (拦截)
 * - 负积分 -> N (拦截)
 *
 * <AUTHOR> Properties Limited
 * @since 1.0
 */

// ========== IP归属地规则 ==========

// 拍照积分：非大陆IP -> N (拦截)
rule "Photo Points - Non-mainland IP - REJECT"
    salience 100
    when
        $context : RiskAssessmentContext(
            businessScenario == RiskAssessmentContext.BusinessScenario.PHOTO_POINTS,
            ipGeolocation != null,
            mallCode == RiskAssessmentContext.MallCode.JAKC || mallCode == RiskAssessmentContext.MallCode.HKC || mallCode == RiskAssessmentContext.MallCode.KP,
            riskResult == null
        )
        $geolocation : IpGeolocation(fromMainlandChina == false) from $context.ipGeolocation
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setBlockMessage("该功能无法使用，请至礼宾台");
        $context.setAssessmentDetails("拍照积分：非大陆IP被拦截");
        System.out.println("Rule executed: Photo Points - Non-mainland IP - REJECT");
end

// ========== 手机号段规则 ==========

// 拍照积分：非大陆手机号 -> N (拦截)
rule "Photo Points - Non-mainland Phone - REJECT"
    salience 90
    when
        $context : RiskAssessmentContext(
            businessScenario == RiskAssessmentContext.BusinessScenario.PHOTO_POINTS,
            isMainlandPhoneNumber == false,
            mallCode == RiskAssessmentContext.MallCode.JAKC || mallCode == RiskAssessmentContext.MallCode.HKC,
            riskResult == null
        )
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setBlockMessage("手机号号段拍照积分受限，请您至礼宾台积分");
        $context.setAssessmentDetails("拍照积分：非大陆手机号被拦截");
        System.out.println("Rule executed: Photo Points - Non-mainland Phone - REJECT");
end

// 虚拟号段：拍照积分 -> N (拦截)
rule "Photo Points - Virtual Phone Number - REJECT"
    salience 90
    when
        $context : RiskAssessmentContext(
            businessScenario == RiskAssessmentContext.BusinessScenario.PHOTO_POINTS,
            isVirtualPhoneNumber == true,
            mallCode == RiskAssessmentContext.MallCode.JAKC || mallCode == RiskAssessmentContext.MallCode.HKC || mallCode == RiskAssessmentContext.MallCode.KP,
            riskResult == null
        )
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setBlockMessage("手机号号段拍照积分受限，请您至礼宾台积分");
        $context.setAssessmentDetails("拍照积分：虚拟号段被拦截");
        System.out.println("Rule executed: Photo Points - Virtual Phone Number - REJECT");
end

// ========== 积分相关规则 ==========

// 拍照积分：负积分 -> N (拦截)
rule "Photo Points - Negative Points - REJECT"
    salience 80
    when
        $context : RiskAssessmentContext(
            businessScenario == RiskAssessmentContext.BusinessScenario.PHOTO_POINTS,
            memberPoints != null,
            memberPoints < 0,
            riskResult == null
        )
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setBlockMessage("TBD");
        $context.setAssessmentDetails("拍照积分：负积分被拦截");
        System.out.println("Rule executed: Photo Points - Negative Points - REJECT");
end
