create table tb_user_form_field_config
(
    id               bigint auto_increment                  not null comment '主键ID',
    created_time     datetime     default current_timestamp not null comment '创建时间',
    updated_time     datetime     default current_timestamp not null on update current_timestamp comment '更新时间',
    user_id          varchar(36)                            not null comment '用户ID',
    service_name     varchar(50)  default ''                not null comment '服务名称',
    form_code        varchar(50)  default ''                not null comment '表单编码',
    form_field_code  varchar(50)  default ''                not null comment '表单字段编码',
    form_field_title varchar(100) default ''                null comment '表单字段标题',
    is_show          tinyint      default 1                 not null comment '是否显示',
    is_fix_field     tinyint      default 0                 not null comment '是否固定列',
    field_order      smallint     default 1                 not null comment '表头顺序',
    primary key (id),
    index idx_user_form (user_id, service_name, form_code)
) comment '用户表单字段配置表';