package com.kerryprops.kip;

import jakarta.persistence.Id;
import org.hibernate.annotations.Generated;
import org.instancio.Instancio;
import org.instancio.Select;
import org.instancio.settings.Keys;
import org.instancio.settings.Settings;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * 随机工具类.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> 2024-03-30 15:23:05
 **/
public final class RandomUtil {

    private static final Settings RANDOM_MODEL_SETTINGS = Settings.create()
            .set(Keys.COLLECTION_MIN_SIZE, 1)
            .set(Keys.COLLECTION_MAX_SIZE, 2);

    private RandomUtil() {
    }

    /**
     * 随机生成对象.
     *
     * @param clazz 对象类型
     * @param <T>   对象类型
     * @return 随机对象
     */
    public static <T> T randomObject(Class<T> clazz) {
        var model = Instancio.of(clazz)
                .withSettings(RANDOM_MODEL_SETTINGS)
                // 排除特定字段
                .ignore(Select.fields().annotated(Id.class))
                .ignore(Select.fields().annotated(Generated.class))
                .ignore(Select.all(MultipartFile.class))
                .lenient()
                .toModel();
        return Instancio.create(model);
    }

    /**
     * 随机生成指定类型的对象列表，默认生成一个对象.
     *
     * @param <T>   对象的类型
     * @param clazz 对象的类类型
     * @return 随机生成的对象列表
     */
    public static <T> List<T> randomObjects(Class<T> clazz) {
        return randomObjects(clazz, 1);
    }

    /**
     * 随机生成指定类型的对象列表.
     *
     * @param <T>   对象的类型
     * @param clazz 对象的类类型
     * @param size  生成对象的数量
     * @return 随机生成的对象列表
     */
    public static <T> List<T> randomObjects(Class<T> clazz, int size) {
        List<T> randomObjects = new ArrayList<>(size);
        for (int i = 0; i < size; i++) {
            randomObjects.add(randomObject(clazz));
        }
        return randomObjects;
    }

}