package com.kerryprops.kip.integration;

import com.kerryprops.kip.ApplicationLauncher;
import com.kerryprops.kip.common.JsonUtils;
import io.restassured.RestAssured;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.http.ContentType;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;

import static io.restassured.config.ObjectMapperConfig.objectMapperConfig;
import static io.restassured.config.RestAssuredConfig.config;

/**
 * BaseIntegrationTest.
 *
 * <AUTHOR> 2025-03-18 17:34:54
 * <AUTHOR> 2025-04-23 16:09:40
 **/
@ActiveProfiles("test")
@SpringBootTest(classes = ApplicationLauncher.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public abstract class BaseIntegrationTest {

    @LocalServerPort
    private int port;

    @BeforeEach
    public void setup() {
        initAssured(port);
    }

    @BeforeAll
    static void setupRedis() {
        RedisHolder.start();
    }

    @DynamicPropertySource
    static void properties(DynamicPropertyRegistry registry) {
        registry.add("spring.data.redis.port", RedisHolder::getRedisPort);
    }

    private void initAssured(int port) {
        RestAssured.reset();
        RestAssured.port = port;
        RestAssured.requestSpecification = new RequestSpecBuilder().setContentType(ContentType.JSON)
                                                                   .build();

        var objectMapperConfig =
                objectMapperConfig().jackson2ObjectMapperFactory((type, s) -> JsonUtils.JACKSON.getObjectMapper());
        RestAssured.config = config().objectMapperConfig(objectMapperConfig);

        RestAssured.enableLoggingOfRequestAndResponseIfValidationFails();
    }

}
