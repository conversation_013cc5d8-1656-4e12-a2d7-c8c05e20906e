package com.kerryprops.kip.integration;

import com.kerryprops.kip.formfield.UserFormFieldConfig;
import com.kerryprops.kip.formfield.UserFormFieldConfigRepository;
import com.kerryprops.kip.formfield.dto.UserFormFieldConfigResetDto;
import com.kerryprops.kip.formfield.vo.UserFormFieldConfigListVo;
import io.restassured.common.mapper.TypeRef;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.kerryprops.kip.RandomUtil.randomObject;
import static io.restassured.RestAssured.given;
import static org.assertj.core.api.Assertions.assertThat;

/**
 * UserFormFiledConfigControllerTest.
 *
 * <AUTHOR> Yu 2025-03-18 17:33:20
 **/
class UserFormFiledConfigControllerIntegrationTest extends BaseIntegrationTest {

    private static final String PATH = "/s/user/form_field_configs";

    @Resource
    UserFormFieldConfigRepository userFormFieldConfigRepository;

    @Test
    @DisplayName("正常查询用户表单配置")
    void listUserFormFieldConfigs_ok() {
        var config = randomObject(UserFormFieldConfig.class);
        userFormFieldConfigRepository.save(config);

        Map<String, Object> params = new HashMap<>();
        params.put("userId", config.getUserId());
        params.put("serviceName", config.getServiceName());
        params.put("formCode", config.getFormCode());
        var result = given().queryParams(params)
                            .when()
                            .get(PATH)
                            .then()
                            .extract()
                            .as(new TypeRef<List<UserFormFieldConfigListVo>>() {

                            });

        assertThat(result).isNotEmpty()
                          .hasSize(1);

        assertThat(result.get(0)).usingRecursiveComparison()
                                 .isEqualTo(config);
    }

    @Test
    @DisplayName("正常重置指定用户表单配置")
    void resetUserFormFieldConfigs_ok() {
        var config = randomObject(UserFormFieldConfig.class);
        userFormFieldConfigRepository.save(config);

        var dto = randomObject(UserFormFieldConfigResetDto.class);
        dto.setUserId(config.getUserId());
        dto.setServiceName(config.getServiceName());
        dto.setFormCode(config.getFormCode());
        var result = given().body(dto)
                            .when()
                            .post(PATH + "/reset")
                            .then()
                            .extract()
                            .as(new TypeRef<List<UserFormFieldConfigListVo>>() {

                            });
        assertThat(result).isNotEmpty();
        assertThat(dto.getFormFields()
                      .get(0)).usingRecursiveComparison()
                              .isEqualTo(result.get(0));
    }

}