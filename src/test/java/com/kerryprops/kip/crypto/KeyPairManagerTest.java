package com.kerryprops.kip.crypto;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.io.File;

import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * ResourceKeyLoaderTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> 2025-04-08 10:00:40
 **/
class KeyPairManagerTest {

    @Test
    @DisplayName("正常生成密钥文件")
    void generateKeyPair_ok() throws Exception {
        // Define temporary file paths for testing
        String publicKeyPath = "test_public_key.pem";
        String privateKeyPath = "test_private_key.pem";

        // Generate key pair
        KeyPairManager.generateKeyPair(publicKeyPath, privateKeyPath);

        // Verify that files exist
        assertTrue(new File(publicKeyPath).exists());
        assertTrue(new File(privateKeyPath).exists());

        // Clean up
        new File(publicKeyPath).delete();
        new File(privateKeyPath).delete();
    }

}