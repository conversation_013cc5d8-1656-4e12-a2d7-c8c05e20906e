package com.kerryprops.kip.formfield;

import com.kerryprops.kip.formfield.dto.UserFormFieldConfigListDto;
import com.kerryprops.kip.formfield.dto.UserFormFieldConfigResetDto;
import com.kerryprops.kip.formfield.vo.UserFormFieldConfigListVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
class UserFormFiledConfigControllerTest {

    @Mock
    private UserFormFieldConfigService userFormFieldConfigService;

    @InjectMocks
    private UserFormFiledConfigController controller;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(controller)
                                 .build();
    }

    @Test
    void getUserFormFieldConfigs_Success() throws Exception {
        // Arrange
        String userId = "testUser";
        String serviceName = "testService";
        String formCode = "testForm";

        List<UserFormFieldConfigListVo> vos = Arrays.asList(createVo(1L, "field1", "Title1", true, false, 1),
                                                            createVo(2L, "field2", "Title2", false, true, 2));

        when(userFormFieldConfigService.listUserFormFieldConfigs(any(UserFormFieldConfigListDto.class))).thenReturn(
                vos);

        // Act & Assert
        mockMvc.perform(MockMvcRequestBuilders.get("/s/user/form_field_configs")
                                              .param("userId", userId)
                                              .param("serviceName", serviceName)
                                              .param("formCode", formCode)
                                              .contentType(MediaType.APPLICATION_JSON))
               .andExpect(status().isOk())
               .andExpect(jsonPath("$[0].formFieldCode").value("field1"))
               .andExpect(jsonPath("$[1].formFieldCode").value("field2"));
    }

    @Test
    void getUserFormFieldConfigs_EmptyResult() throws Exception {
        // Arrange
        when(userFormFieldConfigService.listUserFormFieldConfigs(any(UserFormFieldConfigListDto.class))).thenReturn(
                Collections.emptyList());

        // Act & Assert
        mockMvc.perform(MockMvcRequestBuilders.get("/s/user/form_field_configs")
                                              .param("userId", "user")
                                              .param("serviceName", "service")
                                              .param("formCode", "form"))
               .andExpect(status().isOk())
               .andExpect(jsonPath("$").isArray())
               .andExpect(jsonPath("$").isEmpty());
    }

    @Test
    void resetUserFormFieldConfigs_Success() throws Exception {
        // Arrange
        List<UserFormFieldConfigListVo> vos = Arrays.asList(createVo(1L, "field1", "Title1", true, false, 1),
                                                            createVo(2L, "field2", "Title2", false, true, 2));

        when(userFormFieldConfigService.resetUserFormFieldConfigs(any(UserFormFieldConfigResetDto.class))).thenReturn(
                vos);

        // Act & Assert
        mockMvc.perform(MockMvcRequestBuilders.post("/s/user/form_field_configs/reset")
                                              .contentType(MediaType.APPLICATION_JSON)
                                              .content("""
                                                               {
                                                                   "userId": "testUser",
                                                                   "serviceName": "testService",
                                                                   "formCode": "testForm",
                                                                   "formFields": [
                                                                       {
                                                                           "formFieldCode": "field1",
                                                                           "formFieldTitle": "Title1",
                                                                           "isShow": true,
                                                                           "isFixField": false,
                                                                           "order": 1
                                                                       },
                                                                       {
                                                                           "formFieldCode": "field2",
                                                                           "formFieldTitle": "Title2",
                                                                           "isShow": false,
                                                                           "isFixField": true,
                                                                           "order": 2
                                                                       }
                                                                   ]
                                                               }
                                                               """))
               .andExpect(status().isOk())
               .andExpect(jsonPath("$[0].formFieldCode").value("field1"))
               .andExpect(jsonPath("$[1].formFieldCode").value("field2"));
    }

    @Test
    void resetUserFormFieldConfigs_EmptyRequest() throws Exception {
        // Act & Assert
        mockMvc.perform(MockMvcRequestBuilders.post("/s/user/form_field_configs/reset")
                                              .contentType(MediaType.APPLICATION_JSON)
                                              .content("""
                                                               {
                                                                   "userId": "testUser",
                                                                   "serviceName": "testService",
                                                                   "formCode": "testForm",
                                                                   "formFields": []
                                                               }
                                                               """))
               .andExpect(status().isBadRequest());
    }

    private UserFormFieldConfigListVo createVo(Long id, String code, String title, boolean show, boolean fix,
                                               int order) {
        UserFormFieldConfigListVo vo = new UserFormFieldConfigListVo();
        vo.setId(id);
        vo.setFormFieldCode(code);
        vo.setFormFieldTitle(title);
        vo.setIsShow(show);
        vo.setIsFixField(fix);
        vo.setOrder(order);
        return vo;
    }

}