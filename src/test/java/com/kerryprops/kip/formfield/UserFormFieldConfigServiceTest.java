package com.kerryprops.kip.formfield;

import com.kerryprops.kip.formfield.dto.UserFormFieldConfigListDto;
import com.kerryprops.kip.formfield.dto.UserFormFieldConfigResetDto;
import com.kerryprops.kip.formfield.vo.UserFormFieldConfigListVo;
import com.querydsl.core.types.Predicate;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static com.kerryprops.kip.RandomUtil.randomObject;
import static com.kerryprops.kip.RandomUtil.randomObjects;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class UserFormFieldConfigServiceTest {

    @Mock
    private UserFormFieldConfigRepository userFormFieldConfigRepository;

    @InjectMocks
    private UserFormFieldConfigService userFormFieldConfigService;

    @Test
    @DisplayName("正常查询表单字段列表")
    void listUserFormFieldConfigs_success() {
        // Arrange
        UserFormFieldConfigListDto requestDto = randomObject(UserFormFieldConfigListDto.class);
        var mockEntities = randomObjects(UserFormFieldConfig.class, 2);

        when(userFormFieldConfigRepository.findAll(any(Predicate.class))).thenReturn(mockEntities);

        // Act
        List<UserFormFieldConfigListVo> result = userFormFieldConfigService.listUserFormFieldConfigs(requestDto);

        // Assert
        assertThat(result).hasSize(2);
        verify(userFormFieldConfigRepository, times(1)).findAll(any(Predicate.class));
        verifyNoMoreInteractions(userFormFieldConfigRepository);
    }

    @Test
    @DisplayName("查询表单字段列表-结果为空")
    void listUserFormFieldConfigs_emptyResult() {
        // Arrange
        UserFormFieldConfigListDto requestDto = randomObject(UserFormFieldConfigListDto.class);

        when(userFormFieldConfigRepository.findAll(any(Predicate.class))).thenReturn(List.of());

        // Act
        List<UserFormFieldConfigListVo> result = userFormFieldConfigService.listUserFormFieldConfigs(requestDto);

        // Assert
        assertThat(result).isEmpty();
        verify(userFormFieldConfigRepository, times(1)).findAll(any(Predicate.class));
        verifyNoMoreInteractions(userFormFieldConfigRepository);
    }

    @Test
    @DisplayName("正常重置表单字段配置")
    void resetUserFormFieldConfigs_success() {
        // Arrange
        UserFormFieldConfigResetDto resetDto = randomObject(UserFormFieldConfigResetDto.class);
        int formFieldCount = resetDto.getFormFields().size();

        when(userFormFieldConfigRepository.deleteUserFormFieldConfig(
                resetDto.getUserId(),
                resetDto.getServiceName(),
                resetDto.getFormCode()
        )).thenReturn((long) formFieldCount);

        when(userFormFieldConfigRepository.saveAll(any()))
                .thenReturn(randomObjects(UserFormFieldConfig.class, formFieldCount));

        // Act
        List<UserFormFieldConfigListVo> result = userFormFieldConfigService.resetUserFormFieldConfigs(resetDto);

        // Assert
        assertThat(result).hasSize(formFieldCount);
        verify(userFormFieldConfigRepository, times(1)).deleteUserFormFieldConfig(
                resetDto.getUserId(), resetDto.getServiceName(), resetDto.getFormCode());
        verify(userFormFieldConfigRepository, times(1)).saveAll(any());
        verifyNoMoreInteractions(userFormFieldConfigRepository);
    }

}