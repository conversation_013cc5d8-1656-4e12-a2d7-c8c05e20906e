package com.kerryprops.kip.riskcontrol;

import com.kerryprops.kip.exception.BadRequestException;
import com.kerryprops.kip.riskcontrol.general.*;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import org.instancio.Instancio;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.validation.BindingResult;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Unit tests for the RiskAssessmentController class.
 * Focusing on the phone number validation logic in the controller.
 */
@ExtendWith(MockitoExtension.class)
class RiskAssessmentControllerTest {

    @Mock
    private RiskAssessmentService riskAssessmentService;

    @Mock
    private TencentCloudService tencentCloudService;

    @Mock
    private BindingResult bindingResult;

    @InjectMocks
    private RiskAssessmentController controller;

    @Test
    @DisplayName("assessPhoneNumber - valid request - expected response")
    void assessPhoneNumber_validRequest_expectedResponse() throws TencentCloudSDKException {
        // Arrange
        PhoneTxRiskAssessRequest request = new PhoneTxRiskAssessRequest();
        request.setPhoneNumber("13800138000");
        request.setIp("***********");
        var txRiskResponse = Instancio.create(TxRiskResponse.class);
        when(tencentCloudService.assessPhoneRisk(any())).thenReturn(txRiskResponse);

        // Act
        var response = controller.assessPhoneNumber(request);

        // Assert
        assertNotNull(response);
        assertEquals(txRiskResponse.getRiskResult(), response.getRiskResult());
        verify(tencentCloudService).assessPhoneRisk(request);
    }

    @Test
    @DisplayName("assessPhoneNumbers - invalid request - validation error - throw BadRequest")
    void assessPhoneNumbers_invalidRequest_validationError_badRequest() {
        // Arrange
        PhoneRiskAssessRequest request = Instancio.create(PhoneRiskAssessRequest.class);
        request.setPhoneNumbers(List.of("invalidPhone"));

        when(bindingResult.hasErrors()).thenReturn(true);
        when(bindingResult.getAllErrors()).thenReturn(List.of());

        // Act & Assert
        assertThatThrownBy(() -> controller.assessPhoneNumbers(request, bindingResult)).isInstanceOf(
                BadRequestException.class);

        verify(riskAssessmentService, never()).assessPhoneNumber(any());
    }

    /**
     * Test valid phone numbers with various prefix formats.
     * Should process them using the RiskAssessmentService.
     */
    @ParameterizedTest
    @DisplayName("Should process valid phone numbers with various prefixes")
    @ValueSource(strings = {"12", "1234567890123", "13800138000", "+8613800138000", "8613800138000"})
    void shouldProcessValidPhoneNumbers(String phoneNumber) {
        // Arrange
        var request = new PhoneRiskAssessRequest();
        request.setPhoneNumbers(Collections.singletonList(phoneNumber));

        when(bindingResult.hasErrors()).thenReturn(false);

        var expectedResponse =
                new PhoneRiskAssessResponse(phoneNumber, RiskLevel.LOW, Collections.singletonList(RiskType.NONE),
                        LocalDateTime.now());
        when(riskAssessmentService.assessPhoneNumber(phoneNumber)).thenReturn(expectedResponse);

        // Act
        var results = controller.assessPhoneNumbers(request, bindingResult);

        // Assert
        assertNotNull(results);
        assertEquals(1, results.size());
        assertEquals(phoneNumber, results.get(0)
                .getPhoneNumber());
        assertEquals(RiskLevel.LOW, results.get(0)
                .getRiskLevel());

        // Verify the service was called for valid phone numbers
        verify(riskAssessmentService).assessPhoneNumber(phoneNumber);
    }

    /**
     * Test telephone number validation with more specific cases.
     */
    @ParameterizedTest
    @DisplayName("Should validate phone numbers according to specific rules")
    @CsvSource({"13800138000,true",       // Valid: Standard 11-digit
            "+8613800138000,true",    // Valid: With +86 prefix
            "+861 3800138000,true",    // Valid: With space char
            "8613800138000,true",     // Valid: With 86 prefix (no +)
            "+86-13800138000,false",  // Invalid: Contains hyphen
            "23800138000,false",      // Invalid: Starts with 2 (after prefix)
            "8623800138000,false",    // Invalid: Starts with 2 (after prefix)
            "+8623800138000,false",   // Invalid: Starts with 2 (after prefix)
            "138001380001,true",     // Valid: Extra digits are allowed
            "1380013800,true",       // Valid: Fewer digits are allowed
            "abcdefghijk,false",      // Invalid: Non-numeric
            "+86123,true"           // Valid: Short numbers after prefix are allowed
    })
    void shouldValidatePhoneNumbersAccordingToRules(String phoneNumber, boolean isValid) {
        // Arrange
        var request = new PhoneRiskAssessRequest();
        request.setPhoneNumbers(Collections.singletonList(phoneNumber));

        when(bindingResult.hasErrors()).thenReturn(false);

        if (isValid) {
            var expectedResponse =
                    new PhoneRiskAssessResponse(phoneNumber, RiskLevel.LOW, Collections.singletonList(RiskType.NONE),
                            LocalDateTime.now());
            when(riskAssessmentService.assessPhoneNumber(anyString())).thenReturn(expectedResponse);
        }

        // Act
        var results = controller.assessPhoneNumbers(request, bindingResult);

        // Assert
        assertNotNull(results);
        assertEquals(1, results.size());

        if (isValid) {
            // Verify service was called for valid phone numbers
            verify(riskAssessmentService).assessPhoneNumber(anyString());
        } else {
            // Verify service was NOT called for invalid phone numbers
            // and result has LOW risk level
            assertEquals(RiskLevel.LOW, results.get(0)
                    .getRiskLevel());
            verify(riskAssessmentService, never()).assessPhoneNumber(phoneNumber);
        }
    }

}
