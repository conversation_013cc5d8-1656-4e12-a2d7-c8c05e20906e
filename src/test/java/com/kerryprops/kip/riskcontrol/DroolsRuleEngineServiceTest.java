package com.kerryprops.kip.riskcontrol;

import com.kerryprops.kip.riskcontrol.shared.DroolsRuleEngineService;
import com.kerryprops.kip.riskcontrol.shared.IpGeolocation;
import com.kerryprops.kip.riskcontrol.shared.RiskAssessmentContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit tests for DroolsRuleEngineService.
 * Tests the Drools rules engine integration for comprehensive risk assessment.
 */
@ExtendWith(MockitoExtension.class)
class DroolsRuleEngineServiceTest {

    @Mock
    private KieContainer kieContainer;

    @Mock
    private KieSession kieSession;

    private DroolsRuleEngineService droolsRuleEngineService;

    @BeforeEach
    void setUp() {
        droolsRuleEngineService = new DroolsRuleEngineService(kieContainer);
    }

    @Test
    @DisplayName("Should evaluate risk and return context with results")
    void shouldEvaluateRiskAndReturnContextWithResults() {
        // Given
        String ipAddress = "************";
        IpGeolocation geolocation = IpGeolocation.createValid(ipAddress, "China", "CN")
                .withSubdivision("Beijing")
                .withCity("Beijing")
                .withCoordinates(39.9042, 116.4074);

        RiskAssessmentContext context = RiskAssessmentContext.create(
                RiskAssessmentContext.BusinessScenario.LOGIN,
                RiskAssessmentContext.MallCode.JAKC
        ).withIpInfo(ipAddress, geolocation);

        when(kieContainer.newKieSession()).thenReturn(kieSession);
        when(kieSession.fireAllRules()).thenReturn(1);

        // When
        RiskAssessmentContext result = droolsRuleEngineService.evaluateRisk(context);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getIpAddress()).isEqualTo(ipAddress);
        assertThat(result.getIpGeolocation()).isEqualTo(geolocation);

        verify(kieContainer).newKieSession();
        verify(kieSession).insert(context);
        verify(kieSession).fireAllRules();
        verify(kieSession).dispose();
    }

    @Test
    @DisplayName("Should dispose KieSession even when exception occurs")
    void shouldDisposeKieSessionEvenWhenExceptionOccurs() {
        // Given
        RiskAssessmentContext context = RiskAssessmentContext.create(
                RiskAssessmentContext.BusinessScenario.LOGIN,
                RiskAssessmentContext.MallCode.JAKC
        );

        when(kieContainer.newKieSession()).thenReturn(kieSession);
        when(kieSession.fireAllRules()).thenThrow(new RuntimeException("Rule execution failed"));

        // When & Then
        assertThatThrownBy(() -> droolsRuleEngineService.evaluateRisk(context))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Failed to evaluate risk rules");

        verify(kieSession).dispose();
    }

    @Test
    @DisplayName("Should check rule engine availability successfully")
    void shouldCheckRuleEngineAvailabilitySuccessfully() {
        // Given
        when(kieContainer.newKieSession()).thenReturn(kieSession);

        // When
        boolean isAvailable = droolsRuleEngineService.isRuleEngineAvailable();

        // Then
        assertThat(isAvailable).isTrue();
        verify(kieContainer).newKieSession();
        verify(kieSession).dispose();
    }

    @Test
    @DisplayName("Should return false when rule engine is not available")
    void shouldReturnFalseWhenRuleEngineNotAvailable() {
        // Given
        when(kieContainer.newKieSession()).thenThrow(new RuntimeException("KieContainer not available"));

        // When
        boolean isAvailable = droolsRuleEngineService.isRuleEngineAvailable();

        // Then
        assertThat(isAvailable).isFalse();
        verify(kieContainer).newKieSession();
    }
}
