package com.kerryprops.kip.riskcontrol;

import com.kerryprops.kip.riskcontrol.general.PhoneNumberUtil;
import com.kerryprops.kip.riskcontrol.general.RiskAssessmentService;
import com.kerryprops.kip.riskcontrol.general.RiskLevel;
import com.kerryprops.kip.riskcontrol.general.RiskType;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Tests for the RiskAssessmentService implementation.
 */
@ExtendWith(MockitoExtension.class)
class RiskAssessmentServiceTest {

    @Mock
    private PhoneNumberUtil phoneNumberUtil;

    @InjectMocks
    private RiskAssessmentService riskAssessmentService;

    /**
     * Test assessing a regular (non-virtual) phone number.
     */
    @Test
    @DisplayName("Should return low risk for regular phone numbers")
    void shouldReturnLowRiskForRegularPhoneNumber() {
        // Arrange
        var regularPhoneNumber = "13912345678";
        when(phoneNumberUtil.isValidPhoneNumber(regularPhoneNumber)).thenReturn(true);
        when(phoneNumberUtil.normalizePhoneNumber(regularPhoneNumber)).thenReturn(regularPhoneNumber);
        when(phoneNumberUtil.isVirtualNumber(regularPhoneNumber)).thenReturn(false);

        // Act
        var result = riskAssessmentService.assessPhoneNumber(regularPhoneNumber);

        // Assert
        assertNotNull(result);
        assertEquals(regularPhoneNumber, result.getPhoneNumber());
        assertEquals(RiskLevel.LOW, result.getRiskLevel());
        assertTrue(result.getRiskTypes()
                .contains(RiskType.NONE));
        assertNotNull(result.getLastUpdated());
    }

    /**
     * Test assessing virtual phone numbers from different carriers.
     */
    @ParameterizedTest
    @DisplayName("Should return medium risk for virtual phone numbers")
    @CsvSource({"16512345678,HIGH,FRAUD", "17033456784,HIGH,FRAUD", "15012345678,LOW,NONE"
    })
    void shouldReturnMediumRiskForVirtualPhoneNumbers(String phoneNumber, RiskLevel riskLevel, RiskType riskType) {
        // Arrange
        when(phoneNumberUtil.isValidPhoneNumber(phoneNumber)).thenReturn(true);
        when(phoneNumberUtil.normalizePhoneNumber(phoneNumber)).thenReturn(phoneNumber);
        when(phoneNumberUtil.isVirtualNumber(phoneNumber)).thenReturn(riskLevel == RiskLevel.HIGH);

        // Act
        var result = riskAssessmentService.assessPhoneNumber(phoneNumber);

        // Assert
        assertNotNull(result);
        assertEquals(phoneNumber, result.getPhoneNumber());
        assertEquals(riskLevel, result.getRiskLevel());
        var riskTypes = result.getRiskTypes();
        assertTrue(riskTypes.contains(riskType));
        assertNotNull(result.getLastUpdated());
    }

    /**
     * Test assessing a China Broadcast virtual phone number.
     */
    @Test
    @DisplayName("Should return high risk for China Broadcast virtual phone numbers")
    void shouldReturnHighRiskForChinaBroadcastVirtualPhoneNumbers() {
        // Arrange
        var broadcastPhoneNumber = "19212345678";
        when(phoneNumberUtil.isValidPhoneNumber(broadcastPhoneNumber)).thenReturn(true);
        when(phoneNumberUtil.normalizePhoneNumber(broadcastPhoneNumber)).thenReturn(broadcastPhoneNumber);
        when(phoneNumberUtil.isVirtualNumber(broadcastPhoneNumber)).thenReturn(true);

        // Act
        var result = riskAssessmentService.assessPhoneNumber(broadcastPhoneNumber);

        // Assert
        assertNotNull(result);
        assertEquals(broadcastPhoneNumber, result.getPhoneNumber());
        assertEquals(RiskLevel.HIGH, result.getRiskLevel());
        assertTrue(result.getRiskTypes()
                .contains(RiskType.FRAUD));
        assertNotNull(result.getLastUpdated());
    }

    /**
     * Test assessing a phone number with +86 prefix.
     */
    @Test
    @DisplayName("Should handle phone numbers with +86 prefix")
    void shouldHandlePhoneNumbersWithCountryCode() {
        // Arrange
        var phoneWithPrefix = "+8615012345678";
        var phoneWithoutPrefix = "15012345678";
        when(phoneNumberUtil.isValidPhoneNumber(anyString())).thenReturn(true);
        when(phoneNumberUtil.normalizePhoneNumber(phoneWithPrefix)).thenReturn(phoneWithoutPrefix);
        when(phoneNumberUtil.normalizePhoneNumber(phoneWithoutPrefix)).thenReturn(phoneWithoutPrefix);
        when(phoneNumberUtil.isVirtualNumber(phoneWithoutPrefix)).thenReturn(false);

        // Act
        var resultWithPrefix = riskAssessmentService.assessPhoneNumber(phoneWithPrefix);
        var resultWithoutPrefix = riskAssessmentService.assessPhoneNumber(phoneWithoutPrefix);

        // Assert
        assertEquals(RiskLevel.LOW, resultWithPrefix.getRiskLevel());
        assertEquals(RiskLevel.LOW, resultWithoutPrefix.getRiskLevel());
        assertEquals(phoneWithPrefix, resultWithPrefix.getPhoneNumber());
    }

    /**
     * Test assessing an invalid phone number.
     */
    @Test
    @DisplayName("Should return unknown risk for invalid phone numbers")
    void shouldReturnUnknownRiskForInvalidPhoneNumbers() {
        // Arrange
        var invalidPhoneNumber = "123";
        when(phoneNumberUtil.isValidPhoneNumber(invalidPhoneNumber)).thenReturn(false);

        // Act
        var result = riskAssessmentService.assessPhoneNumber(invalidPhoneNumber);

        // Assert
        assertNotNull(result);
        assertEquals(invalidPhoneNumber, result.getPhoneNumber());
        assertEquals(RiskLevel.UNKNOWN, result.getRiskLevel());
        assertTrue(result.getRiskTypes()
                .contains(RiskType.NONE));
        assertNotNull(result.getLastUpdated());
    }

    /**
     * Test assessing multiple phone numbers.
     */
    @Test
    @DisplayName("Should assess multiple phone number")
    void shouldAssessMultiplePhoneNumbers() {
        // Arrange
        var phoneNumber = "19212345678";

        when(phoneNumberUtil.isValidPhoneNumber(anyString())).thenReturn(true);
        when(phoneNumberUtil.normalizePhoneNumber(anyString())).thenAnswer(i -> i.getArgument(0));
        when(phoneNumberUtil.isVirtualNumber(anyString())).thenReturn(false);
        when(phoneNumberUtil.isVirtualNumber("19212345678")).thenReturn(true);

        // Act
        var result = riskAssessmentService.assessPhoneNumber(phoneNumber);

        // Assert
        assertNotNull(result);

        assertEquals(RiskLevel.HIGH, result.getRiskLevel());
    }

    /**
     * Test handling empty phone number list.
     */
    @Test
    @DisplayName("Should return empty list for empty phone number list")
    void shouldReturnEmptyListForEmptyPhoneNumberList() {
        // Act
        var result = riskAssessmentService.assessPhoneNumber("");

        // Assert
        assertNotNull(result);
        assertEquals(RiskLevel.UNKNOWN, result.getRiskLevel(), "Risk level should be low for empty phone number list");
    }

}
