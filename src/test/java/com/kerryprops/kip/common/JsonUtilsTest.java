package com.kerryprops.kip.common;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.Getter;
import lombok.Setter;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * JsonUtils工具类的单元测试类.
 *
 * <p>
 * 测试JSON序列化和反序列化的各种场景，包括基本对象、集合、日期时间类型以及边界情况.
 * </p>
 *
 * <AUTHOR> Zhang
 * @since 1.0
 */
class JsonUtilsTest {

    @Test
    @DisplayName("测试将 Java 对象转换为 JSON 字符串")
    void toJson_Success() {
        // Arrange
        var testObject = new TestObject("test", 123);

        // Act
        var result = JsonUtils.toJson(testObject);

        // Assert
        assertEquals("{\"name\":\"test\",\"value\":123}", result);
    }

    @Test
    @DisplayName("测试将 JSON 字符串转换为 Java 对象")
    void fromJson_Success() {
        // Arrange
        var json = "{\"name\":\"test\",\"value\":123}";

        // Act
        var result = JsonUtils.fromJson(json, TestObject.class);

        // Assert
        assertTrue(result.isPresent());
        var testObject = result.get();
        assertEquals("test", testObject.getName());
        assertEquals(123, testObject.getValue());
    }

    @Test
    @DisplayName("测试将空 JSON 字符串转换为 Java 对象返回空 Optional")
    void fromJson_EmptyString_ReturnsEmptyOptional() {
        // Arrange
        var json = "";

        // Act
        var result = JsonUtils.fromJson(json, TestObject.class);

        // Assert
        assertFalse(result.isPresent());
    }

    @Test
    @DisplayName("测试将无效 JSON 字符串转换为 Java 对象返回空 Optional")
    void fromJson_InvalidJson_ReturnsEmptyOptional() {
        // Arrange
        var json = "{invalid json}";

        // Act
        var result = JsonUtils.fromJson(json, TestObject.class);

        // Assert
        assertFalse(result.isPresent());
    }

    @Test
    @DisplayName("测试将 JSON 字符串转换为集合类型")
    void fromJsonV2_ListType_Success() {
        // Arrange
        var json = "[{\"name\":\"test1\",\"value\":123},{\"name\":\"test2\",\"value\":456}]";

        // Act
        var result = JsonUtils.fromJsonV2(json,
                new TypeReference<List<TestObject>>() {

                });

        // Assert
        assertTrue(result.isPresent());
        var testObjects = result.get();
        assertEquals(2, testObjects.size());
        assertEquals("test1", testObjects.get(0).getName());
        assertEquals(123, testObjects.get(0).getValue());
        assertEquals("test2", testObjects.get(1).getName());
        assertEquals(456, testObjects.get(1).getValue());
    }

    @Test
    @DisplayName("测试将空 JSON 字符串转换为集合类型返回空 Optional")
    void fromJsonV2_EmptyString_ReturnsEmptyOptional() {
        // Arrange
        var json = "";

        // Act
        Optional<List<TestObject>> result = JsonUtils.fromJsonV2(json,
                new TypeReference<>() {

                });

        // Assert
        assertFalse(result.isPresent());
    }

    @Test
    @DisplayName("测试将 JSON 字符串转换为 LocalDateTime 类型")
    void toJson_LocalDateTime_Success() {
        // Arrange
        var dateTime = LocalDateTime.of(2023, 1, 1, 12, 30, 45);
        var wrapper = new DateTimeWrapper();
        wrapper.setDateTime(dateTime);

        // Act
        var json = JsonUtils.toJson(wrapper);
        var result = JsonUtils.fromJson(json, DateTimeWrapper.class);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(dateTime, result.get().getDateTime());
    }

    @Test
    @DisplayName("测试将 JSON 字符串转换为 LocalDate 类型")
    void toJson_LocalDate_Success() {
        // Arrange
        var date = LocalDate.of(2023, 1, 1);
        var wrapper = new DateWrapper();
        wrapper.setDate(date);

        // Act
        var json = JsonUtils.toJson(wrapper);
        var result = JsonUtils.fromJson(json, DateWrapper.class);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(date, result.get().getDate());
    }

    @Test
    @DisplayName("测试将 JSON 字符串转换为 LocalTime 类型")
    void toJson_LocalTime_Success() {
        // Arrange
        var time = LocalTime.of(12, 30, 45);
        var wrapper = new TimeWrapper();
        wrapper.setTime(time);

        // Act
        var json = JsonUtils.toJson(wrapper);
        var result = JsonUtils.fromJson(json, TimeWrapper.class);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(time, result.get().getTime());
    }

    @Getter
    @Setter
    private static final class TestObject {

        private final String name;

        private final int value;

        TestObject(String name, int value) {
            this.name = name;
            this.value = value;
        }

    }

    @Getter
    @Setter
    private static final class DateTimeWrapper {

        private LocalDateTime dateTime;

    }

    @Getter
    @Setter
    private static final class DateWrapper {

        private LocalDate date;

    }

    @Getter
    @Setter
    private static final class TimeWrapper {

        private LocalTime time;

    }

}
