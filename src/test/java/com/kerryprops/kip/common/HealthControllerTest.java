package com.kerryprops.kip.common;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthContributorRegistry;
import org.springframework.boot.actuate.health.HealthIndicator;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

/**
 * Unit test for HealthController.
 * Covers all branches and exception cases.
 */
class HealthControllerTest {
    @Mock
    private HealthContributorRegistry healthRegistry;

    @Mock
    private HealthIndicator dbHealthIndicator;
    @Mock
    private HealthIndicator redisHealthIndicator;

    @InjectMocks
    private HealthController healthController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        healthController = new HealthController(healthRegistry);
    }

    @Test
    @DisplayName("should return SUCCESS_RESPONSE when db and redis are UP")
    void healthProxy_allUp() {
        // Arrange
        when(healthRegistry.getContributor("db")).thenReturn(dbHealthIndicator);
        when(healthRegistry.getContributor("redis")).thenReturn(redisHealthIndicator);
        when(dbHealthIndicator.health()).thenReturn(Health.up().build());
        when(redisHealthIndicator.health()).thenReturn(Health.up().build());
        // Act
        var result = healthController.healthProxy();
        // Assert
        assertThat(result).isEqualTo(HealthController.HealthDto.SUCCESS_RESPONSE);
    }

    @Test
    @DisplayName("should return FAIL_RESPONSE when db is DOWN")
    void healthProxy_dbDown() {
        // Arrange
        when(healthRegistry.getContributor("db")).thenReturn(dbHealthIndicator);
        when(healthRegistry.getContributor("redis")).thenReturn(redisHealthIndicator);
        when(dbHealthIndicator.health()).thenReturn(Health.down().build());
        when(redisHealthIndicator.health()).thenReturn(Health.up().build());
        // Act
        var result = healthController.healthProxy();
        // Assert
        assertThat(result).isEqualTo(HealthController.HealthDto.FAIL_RESPONSE);
    }

    @Test
    @DisplayName("should return FAIL_RESPONSE when redis is DOWN")
    void healthProxy_redisDown() {
        // Arrange
        when(healthRegistry.getContributor("db")).thenReturn(dbHealthIndicator);
        when(healthRegistry.getContributor("redis")).thenReturn(redisHealthIndicator);
        when(dbHealthIndicator.health()).thenReturn(Health.up().build());
        when(redisHealthIndicator.health()).thenReturn(Health.down().build());
        // Act
        var result = healthController.healthProxy();
        // Assert
        assertThat(result).isEqualTo(HealthController.HealthDto.FAIL_RESPONSE);
    }

    @Test
    @DisplayName("should return FAIL_RESPONSE and log error when exception occurs")
    void healthProxy_exception() {
        // Arrange
        when(healthRegistry.getContributor("db")).thenThrow(new RuntimeException("db error"));
        // Act
        var result = healthController.healthProxy();
        // Assert
        assertThat(result).isEqualTo(HealthController.HealthDto.FAIL_RESPONSE);
    }
}
