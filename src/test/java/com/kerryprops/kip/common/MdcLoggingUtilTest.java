package com.kerryprops.kip.common;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.MDC;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

/**
 * Unit test for MdcLoggingUtil.
 * Covers all branches and exception cases.
 */
class MdcLoggingUtilTest {

    @AfterEach
    void tearDown() {
        // Always clear MDC after each test.
        MdcLoggingUtil.clearCorrelationIdFromContext();
    }

    @Test
    @DisplayName("should generate and set correlationId when input is blank")
    void initCorrelationId_blank() {
        // Act
        MdcLoggingUtil.initCorrelationId("");
        // Assert
        var correlationId = MDC.get(AppConstants.MDC_CORRELATION_ID_KEY);
        assertThat(StringUtils.isNotBlank(correlationId)).isTrue();
        assertThat(correlationId).startsWith(AppConstants.APP_PREFIX);
    }

    @Test
    @DisplayName("should set correlationId when input is not blank")
    void initCorrelationId_notBlank() {
        // Arrange
        var id = "test-correlation-id";
        // Act
        MdcLoggingUtil.initCorrelationId(id);
        // Assert
        assertThat(MDC.get(AppConstants.MDC_CORRELATION_ID_KEY)).isEqualTo(id);
    }

    @Test
    @DisplayName("should retrieve current correlationId")
    void retrieveCurrentCorrelationId() {
        // Arrange
        var id = "retrieve-id";
        MDC.put(AppConstants.MDC_CORRELATION_ID_KEY, id);
        // Act
        var result = MdcLoggingUtil.retrieveCurrentCorrelationId();
        // Assert
        assertThat(result).isEqualTo(id);
    }

    @Test
    @DisplayName("should clear correlationId from context")
    void clearCorrelationIdFromContext() {
        // Arrange
        MDC.put(AppConstants.MDC_CORRELATION_ID_KEY, "to-be-cleared");
        // Act
        MdcLoggingUtil.clearCorrelationIdFromContext();
        // Assert
        assertThat(MDC.get(AppConstants.MDC_CORRELATION_ID_KEY)).isNull();
    }

    @Test
    @DisplayName("should extract and set correlationId from request header (header present)")
    void extractAndSetCorrelationIdFromRequest_headerPresent() {
        // Arrange
        var request = mock(HttpServletRequest.class);
        when(request.getHeader("X-Correlation-ID")).thenReturn("header-id");
        // Act
        MdcLoggingUtil.extractAndSetCorrelationIdFromRequest(request);
        // Assert
        assertThat(MDC.get(AppConstants.MDC_CORRELATION_ID_KEY)).isEqualTo("header-id");
    }

    @Test
    @DisplayName("should extract and set correlationId from request header (header blank)")
    void extractAndSetCorrelationIdFromRequest_headerBlank() {
        // Arrange
        var request = mock(HttpServletRequest.class);
        when(request.getHeader("X-Correlation-ID")).thenReturn("");
        // Act
        MdcLoggingUtil.extractAndSetCorrelationIdFromRequest(request);
        // Assert
        var correlationId = MDC.get(AppConstants.MDC_CORRELATION_ID_KEY);
        assertThat(StringUtils.isNotBlank(correlationId)).isTrue();
        assertThat(correlationId).startsWith(AppConstants.APP_PREFIX);
    }

    @Test
    @DisplayName("should propagate correlationId to response when present")
    void propagateCorrelationIdToResponse_present() {
        // Arrange
        var response = mock(HttpServletResponse.class);
        MDC.put(AppConstants.MDC_CORRELATION_ID_KEY, "propagate-id");
        // Act
        MdcLoggingUtil.propagateCorrelationIdToResponse(response);
        // Assert
        verify(response).setHeader("X-Correlation-ID", "propagate-id");
    }

    @Test
    @DisplayName("should not propagate correlationId to response when blank")
    void propagateCorrelationIdToResponse_blank() {
        // Arrange
        var response = mock(HttpServletResponse.class);
        MDC.remove(AppConstants.MDC_CORRELATION_ID_KEY);
        // Act
        MdcLoggingUtil.propagateCorrelationIdToResponse(response);
        // Assert
        verify(response, never()).setHeader(anyString(), anyString());
    }

    @Test
    @DisplayName("should initialize correlationId with value (calls initCorrelationId with null)")
    void initializeCorrelationIdWithValue() {
        // Act
        MdcLoggingUtil.initializeCorrelationIdWithValue();
        // Assert
        var correlationId = MDC.get(AppConstants.MDC_CORRELATION_ID_KEY);
        assertThat(StringUtils.isNotBlank(correlationId)).isTrue();
        assertThat(correlationId).startsWith(AppConstants.APP_PREFIX);
    }
}
