# List Query Customizer

## 项目简介

List Query Customizer 是一个查询和列表自定义动态展示通用服务，旨在提供灵活的表单字段配置管理功能。该项目允许用户根据不同的服务和表单类型自定义字段配置，从而实现动态化的用户界面展示和查询功能。

## 功能特点

- **用户表单字段配置管理**：支持按用户ID、服务名称和表单代码查询表单字段配置
- **配置重置功能**：支持重置用户的表单字段配置
- **分布式锁支持**：通过Redisson实现分布式锁机制，确保数据一致性
- **JPA查询增强**：自定义JPA适配器，提供更灵活的查询能力

## 技术栈

- **基础框架**：Spring Boot 3.2.12
- **JDK版本**：Java 17
- **构建工具**：Maven
- **数据访问**：Spring Data JPA
- **缓存与分布式锁**：Redisson
- **API文档**：SpringDoc OpenAPI
- **日志系统**：Log4j2
- **测试框架**：JUnit 5, Rest Assured
- **代码质量**：Checkstyle, JaCoCo

## 安装与使用

### 环境要求

- JDK 17+
- Maven 3.6+
- Redis (用于分布式锁)
- 关系型数据库 (如MySQL, PostgreSQL等)

### 构建项目

```bash
mvn clean package
```

### 运行应用

```bash
java -jar target/list-query-customizer-1.0.0.jar
```

或者使用Spring Boot Maven插件:

```bash
mvn spring-boot:run
```

### Docker部署

项目提供了Dockerfile，可以通过以下命令构建Docker镜像:

```bash
docker build -t list-query-customizer:1.0.0 .
```

运行Docker容器:

```bash
docker run -p 8080:8080 list-query-customizer:1.0.0
```

## API接口

项目提供了RESTful API接口，主要包括：

- 获取用户表单字段配置列表
- 重置用户表单字段配置

详细的API文档可通过启动应用后访问Swagger UI界面获取：<http://localhost:8080/swagger-ui.html>

## 开发规范

- 遵循CLEAN/SOLID/DRY原则和企业级web开发标准
- 代码注释使用英文
- 测试采用AAA模式
- 方法级Javadoc必写
- 尽可能使用lambda表达式、var关键字和Stream API使代码更简洁
- 使用Optional避免空指针异常

## 开发团队

Kerry Properties Development Team | Ocean  
Email: [<EMAIL>](mailto:<EMAIL>)  
Organization: Kerry Properties  
Website: [https://www.kerryprops.com](https://www.kerryprops.com)

## 许可证

(c) Kerry Properties Limited. All rights reserved.
