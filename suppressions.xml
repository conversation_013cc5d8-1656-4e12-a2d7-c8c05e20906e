<?xml version="1.0"?>
<!DOCTYPE suppressions PUBLIC
        "-//Checkstyle//DTD SuppressionFilter Configuration 1.2//EN"
        "https://checkstyle.org/dtds/suppressions_1_2.dtd">
<suppressions>
    <!-- 
       Checkstyle 抑制配置 v1.0
       最后更新: 2025-03-25
       维护者: Sam <<EMAIL>>
       
       此文件定义了针对特定文件或模式的 checkstyle 规则例外。
       更改应由架构团队审核。
     -->
    <!-- 包文档抑制 - 仅适用于 package-info.java -->
    <suppress files="^(?!.*package-info\.java).*$" checks="JavadocPackage"/>
    <suppress files="^(?!.*package-info\.java).*$" checks="RegexpSinglelineJava" id="checkNonNullApi"/>
    <suppress files="^(?!.*package-info\.java).*$" checks="RegexpSinglelineJava" id="checkNonNullFields"/>
    <suppress files="^(?!.*package-info\.java).*$" checks="RegexpSinglelineJava" id="checkNonNullApiImport"/>
    <suppress files="^(?!.*package-info\.java).*$" checks="RegexpSinglelineJava" id="checkNonNullFieldsImport"/>
    <!-- 特定文件异常捕获抑制 -->
    <suppress files="LockAspect.java|LockExecutor.java|HealthController.java|RsaCryptoService.java|IpRiskAssessmentService.java|DroolsRuleEngineService.java"
              checks="IllegalCatch"/>
</suppressions>